import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu xóa nhiều đơn hàng
 */
export class BulkDeleteUserOrderDto {
  /**
   * Danh sách ID đơn hàng cần xóa
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID đơn hàng cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'Danh sách ID đơn hàng phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một đơn hàng để xóa' })
  @ArrayUnique({ message: 'Danh sách ID đơn hàng không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID đơn hàng phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: '<PERSON><PERSON> sách ID đơn hàng không được để trống' })
  orderIds: number[];
}
