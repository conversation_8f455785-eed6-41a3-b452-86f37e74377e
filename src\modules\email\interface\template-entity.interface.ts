import { AffiliateAccount, AffiliateClick, AffiliateContract, AffiliateCustomerOrder, AffiliatePointConversionHistory, AffiliateWithdrawHistory } from "@modules/affiliate";
import { Blog, BlogComment, BlogPurchase } from "@/modules/blog/entities";
import { AdminTemplateEmail } from "@/modules/marketing/admin/entities";
import { AuthVerificationLog, Bank, BusinessInfo } from "@/modules/user/entities";

/**
 * Interface đại diện cho tập hợp các entity có thể được sử dụng
 * làm nguồn dữ liệu khi tạo nội dung email từ template.
 * Các thuộc tính là tùy chọn, chỉ cần cung cấp entity liên quan đến template.
 */
export interface TemplateEntityData {
    adminTemplateEmail?: AdminTemplateEmail;
    affiliateAccount?: AffiliateAccount;
    affiliateClick?: AffiliateClick;
    affiliateContract?: AffiliateContract;
    affiliateCustomerOrder?: AffiliateCustomerOrder;
    affiliatePointConversionHistory?: AffiliatePointConversionHistory;
    affiliateWithdrawHistory?: AffiliateWithdrawHistory;
    authVerificationLog?: AuthVerificationLog;
    bank?: Bank;
    blogComment?: BlogComment;
    blogPurchase?: BlogPurchase;
    blog?: Blog;
    businessInfo?: BusinessInfo;

    // Có thể thêm các thuộc tính dữ liệu khác không phải entity nếu cần
    [key: string]: any;
}