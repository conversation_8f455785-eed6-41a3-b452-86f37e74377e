import { Injectable, Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { CONFIG_CONSTANTS, ConfigType, Environment } from './constants';
import {
  AppConfig,
  AuthConfig,
  DatabaseConfig,
  EmailConfig,
  FacebookConfig,
  RedisConfig,
  S3Config,
  SecretKeyModelConfig,
  ShipmentConfig,
  StorageConfig,
} from './interfaces';

/**
 * Service cung cấp các phương thức để truy cập cấu hình ứng dụng
 */
@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);
  private config: AppConfig;

  constructor(private readonly nestConfigService: NestConfigService) {
    this.initializeConfig();
  }

  /**
   * Khởi tạo cấu hình <PERSON>ng dụng
   */
  private initializeConfig(): void {
    this.config = {
      port: this.nestConfigService.get<number>(
        'PORT',
        CONFIG_CONSTANTS.DEFAULTS.PORT,
      ),
      nodeEnv: this.nestConfigService.get<string>(
        'NODE_ENV',
        CONFIG_CONSTANTS.DEFAULTS.NODE_ENV,
      ),
      apiPrefix: this.nestConfigService.get<string>(
        'API_PREFIX',
        CONFIG_CONSTANTS.DEFAULTS.API_PREFIX,
      ),

      database: this.getDatabaseConfig(),
      storage: this.getStorageConfig(),
      auth: this.getAuthConfig(),
      facebook: this.getFacebookConfig(),
      email: this.getEmailConfig(),
      redis: this.getRedisConfig(),
      s3: this.getS3Config(),
      secrectKeyModel: this.getSecretKeyModelConfig(),
      shipment: this.getShipmentConfig(),
    };

    this.logger.log(
      `Application configured for environment: ${this.config.nodeEnv}`,
    );
  }

  private getSecretKeyModelConfig(): SecretKeyModelConfig {
    try {
      return {
        adminSecretKey: this.nestConfigService.getOrThrow<string>(
          'ADMIN_SECRECT_MODEL',
        ),
        userSecretKey:
          this.nestConfigService.getOrThrow<string>('USER_SECRECT_MODEL'),
      };
    } catch (error) {
      this.logger.error(
        'Lỗi khi lấy cấu hình secret key model:',
        error.message,
      );
      // Sử dụng giá trị mặc định nếu không tìm thấy trong biến môi trường
      return {
        adminSecretKey: 'default_admin_secret_key',
        userSecretKey: 'default_user_secret_key',
      };
    }
  }

  /**
   * Lấy cấu hình database
   */
  private getDatabaseConfig(): DatabaseConfig {
    return {
      host: this.nestConfigService.getOrThrow<string>('DB_HOST'),
      port: this.nestConfigService.get<number>('DB_PORT', 5432),
      username: this.nestConfigService.getOrThrow<string>('DB_USERNAME'),
      password: this.nestConfigService.getOrThrow<string>('DB_PASSWORD'),
      database: this.nestConfigService.getOrThrow<string>('DB_DATABASE'),
      ssl: this.nestConfigService.get<boolean>('DB_SSL', false),
    };
  }

  /*
   * Lấy cấu hình s3
   */
  private getS3Config(): S3Config {
    return {
      s3: {
        endpoint: this.nestConfigService.getOrThrow<string>('CF_R2_ENDPOINT'),
        region: this.nestConfigService.getOrThrow<string>('CF_R2_REGION'),
        accessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_ACCESS_KEY'),
        secretAccessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_SECRET_KEY'),
        bucketName: this.nestConfigService.getOrThrow<string>('CF_BUCKET_NAME'),
      },
      cdn: {
        url: this.nestConfigService.getOrThrow<string>('CDN_URL'),
        secretKey: this.nestConfigService.getOrThrow<string>('CDN_SECRET_KEY'),
      },
    };
  }

  /**
   * Lấy cấu hình storage
   */
  private getStorageConfig(): StorageConfig {
    return {
      cloudflare: {
        region: this.nestConfigService.getOrThrow<string>('CF_R2_REGION'),
        accessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_ACCESS_KEY'),
        secretKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_SECRET_KEY'),
        endpoint: this.nestConfigService.getOrThrow<string>('CF_R2_ENDPOINT'),
        bucketName: this.nestConfigService.getOrThrow<string>('CF_BUCKET_NAME'),
      },
      cdn: {
        url: this.nestConfigService.getOrThrow<string>('CDN_URL'),
        secretKey: this.nestConfigService.getOrThrow<string>('CDN_SECRET_KEY'),
      },
    };
  }

  /**
   * Lấy cấu hình authentication
   */
  private getAuthConfig(): AuthConfig {
    return {
      jwt: {
        secret: this.nestConfigService.getOrThrow<string>('JWT_SECRET'),
        expirationTime: this.nestConfigService.get<string>(
          'JWT_EXPIRATION_TIME',
          '1d',
        ),
        refreshSecret: this.nestConfigService.get<string>('JWT_REFRESH_SECRET'),
        refreshExpirationTime: this.nestConfigService.get<string>(
          'JWT_REFRESH_EXPIRATION_TIME',
          '7d',
        ),
      },
    };
  }

  /**
   * Lấy cấu hình facebook
   */
  private getFacebookConfig(): FacebookConfig {
    try {
      const config = {
        appId: this.nestConfigService.get<string>(
          'FACEBOOK_APP_ID',
          'mock_facebook_app_id',
        ),
        appSecret: this.nestConfigService.get<string>(
          'FACEBOOK_APP_SECRET',
          'mock_facebook_app_secret',
        ),
        graphApiVersion: this.nestConfigService.get<string>(
          'FACEBOOK_GRAPH_API_VERSION',
          'v18.0',
        ),
        redirectUri: this.nestConfigService.get<string>(
          'FACEBOOK_REDIRECT_URI',
          'http://localhost:3000/auth/facebook/callback',
        ),
      };

      // Log cấu hình Facebook được load
      this.logger.log(`[DEBUG] Facebook config loaded:`, {
        hasAppId: !!config.appId,
        hasAppSecret: !!config.appSecret,
        appId: config.appId ? `${config.appId.substring(0, 15)}...` : 'null',
        appSecret: config.appSecret ? `${config.appSecret.substring(0, 15)}...` : 'null',
        graphApiVersion: config.graphApiVersion,
        redirectUri: config.redirectUri,
        isMockConfig: config.appId?.includes('mock') || config.appSecret?.includes('mock'),
      });

      return config;
    } catch (error) {
      this.logger.warn(
        'Sử dụng cấu hình Facebook mặc định vì thiếu biến môi trường',
      );
      const fallbackConfig = {
        appId: 'mock_facebook_app_id',
        appSecret: 'mock_facebook_app_secret',
        graphApiVersion: 'v18.0',
        redirectUri: 'http://localhost:3000/auth/facebook/callback',
      };

      this.logger.log(`[DEBUG] Facebook fallback config:`, fallbackConfig);
      return fallbackConfig;
    }
  }

  /**
   * Lấy cấu hình email
   */
  private getEmailConfig(): EmailConfig {
    return {
      apiUrl: this.nestConfigService.get<string>('EMAIL_API_URL'),
      smtpHost: this.nestConfigService.get<string>('EMAIL_SMTP_HOST'),
      smtpPort: this.nestConfigService.get<number>('EMAIL_SMTP_PORT'),
      smtpUser: this.nestConfigService.get<string>('EMAIL_SMTP_USER'),
      smtpPass: this.nestConfigService.get<string>('EMAIL_SMTP_PASS'),
    };
  }

  /**
   * Lấy cấu hình redis
   */
  private getRedisConfig(): RedisConfig {
    return {
      url: this.nestConfigService.getOrThrow<string>('REDIS_URL'),
      password: this.nestConfigService.get<string>('REDIS_PASSWORD'),
    };
  }

  private getShipmentConfig(): ShipmentConfig {
    return {
      ghtk: this.nestConfigService.getOrThrow<string>('GHTK_ENCRYPTION_KEY'),
      ghn: this.nestConfigService.getOrThrow<string>('GHN_ENCRYPTION_KEY'),
    };
  }

  /**
   * Lấy toàn bộ cấu hình ứng dụng
   */
  get appConfig(): AppConfig {
    return this.config;
  }

  /**
   * Lấy cấu hình theo loại
   * @param type Loại cấu hình
   */
  getConfig<T>(type: ConfigType): T {
    switch (type) {
      case ConfigType.App:
        return {
          port: this.config.port,
          nodeEnv: this.config.nodeEnv,
          apiPrefix: this.config.apiPrefix,
        } as unknown as T;
      case ConfigType.Database:
        return this.config.database as unknown as T;
      case ConfigType.Storage:
        return this.config.storage as unknown as T;
      case ConfigType.Auth:
        return this.config.auth as unknown as T;
      case ConfigType.Email:
        return this.config.email as unknown as T;
      case ConfigType.Redis:
        return this.config.redis as unknown as T;
      case ConfigType.S3:
        return this.config.s3 as unknown as T;
      case ConfigType.SecretKeyModel:
        return this.config.secrectKeyModel as unknown as T;
      case ConfigType.Facebook:
        return this.config.facebook as unknown as T;
      case ConfigType.Shipment:
        return this.config.shipment as unknown as T;
      default:
        throw new Error(`Unknown config type: ${type}`);
    }
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường development không
   */
  isDevelopment(): boolean {
    return this.config.nodeEnv === Environment.Development;
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường production không
   */
  isProduction(): boolean {
    return this.config.nodeEnv === Environment.Production;
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường test không
   */
  isTest(): boolean {
    return this.config.nodeEnv === Environment.Test;
  }
}
