import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsNumber, IsOptional, IsString, MinLength } from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    description: 'Tên đầy đủ của người dùng',
    example: 'Nguyễn <PERSON>ăn <PERSON>',
  })
  @IsString({ message: 'Tên đầy đủ phải là chuỗi' })
  @IsNotEmpty({ message: 'Tên đầy đủ không được để trống' })
  fullName: string;

  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  @IsNotEmpty({ message: 'Email không được để trống' })
  email: string;

  @ApiProperty({
    description: 'Mật khẩu của người dùng',
    example: 'password123',
  })
  @IsString({ message: 'M<PERSON>t khẩu phải là chuỗi' })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password: string;

  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0912345678',
    required: true,
  })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  phoneNumber: string;

  @ApiProperty({
    description: 'Token reCAPTCHA',
    example: '03AGdBq24PBCbwiDRVu...',
  })
  @IsString({ message: 'Token reCAPTCHA phải là chuỗi' })
  @IsOptional({ message: 'Token reCAPTCHA không được để trống' })
  recaptchaToken: string;

  @ApiProperty({
    description: 'Mã quốc gia của số điện thoại',
    example: '+84',
    default: '+84',
  })
  @IsString({ message: 'Mã quốc gia phải là chuỗi' })
  @IsOptional()
  countryCode?: string = '+84';

  @ApiProperty({
    description: 'Mã người giới thiệu (tùy chọn)',
    example: 12345,
    required: false,
  })
  @IsNumber({}, { message: 'Mã người giới thiệu phải là số' })
  @IsOptional()
  ref?: number;
}
