import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity cho hiệu suất <PERSON>alo <PERSON>s
 */
@Entity('zalo_ads_performance')
export class ZaloAdsPerformance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'ads_account_id' })
  adsAccountId: string;

  @Column({ name: 'campaign_id' })
  campaignId: string;

  @Column({ name: 'date_start', type: 'date' })
  dateStart: string;

  @Column({ name: 'date_end', type: 'date' })
  dateEnd: string;

  @Column({ name: 'impressions', type: 'bigint', default: 0 })
  impressions: number;

  @Column({ name: 'clicks', type: 'bigint', default: 0 })
  clicks: number;

  @Column({ name: 'spend', type: 'decimal', precision: 15, scale: 2, default: 0 })
  spend: number;

  @Column({ name: 'revenue', type: 'decimal', precision: 15, scale: 2, default: 0 })
  revenue: number;

  @Column({ name: 'conversions', type: 'int', default: 0 })
  conversions: number;

  @Column({ name: 'ctr', type: 'decimal', precision: 5, scale: 2, default: 0 })
  ctr: number;

  @Column({ name: 'cpc', type: 'decimal', precision: 15, scale: 2, default: 0 })
  cpc: number;

  @Column({ name: 'cpm', type: 'decimal', precision: 15, scale: 2, default: 0 })
  cpm: number;

  @Column({ name: 'roas', type: 'decimal', precision: 5, scale: 2, default: 0 })
  roas: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
