import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloAdsAccount } from '../entities/zalo-ads-account.entity';

/**
 * Repository cho ZaloAdsAccount
 */
@Injectable()
export class ZaloAdsAccountRepository {
  constructor(
    @InjectRepository(ZaloAdsAccount)
    private readonly repository: Repository<ZaloAdsAccount>,
  ) {}

  /**
   * Tìm kiếm nhiều Ads Account
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách Ads Account
   */
  async find(options?: FindManyOptions<ZaloAdsAccount>): Promise<ZaloAdsAccount[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một Ads Account
   * @param options Tùy chọn tìm kiếm
   * @returns Ads Account hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloAdsAccount>): Promise<ZaloAdsAccount | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm Ads Account theo ID
   * @param id ID của Ads Account
   * @returns Ads Account hoặc null
   */
  async findById(id: number): Promise<ZaloAdsAccount | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm Ads Account theo ID người dùng và ID Ads Account
   * @param userId ID của người dùng
   * @param adsAccountId ID của Ads Account trên Zalo
   * @returns Ads Account hoặc null
   */
  async findByUserIdAndAdsAccountId(userId: number, adsAccountId: string): Promise<ZaloAdsAccount | null> {
    return this.repository.findOne({ where: { userId, adsAccountId } });
  }

  /**
   * Tìm tất cả Ads Account của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách Ads Account
   */
  async findByUserId(userId: number): Promise<ZaloAdsAccount[]> {
    return this.repository.find({ where: { userId } });
  }

  /**
   * Tạo mới Ads Account
   * @param data Dữ liệu Ads Account
   * @returns Ads Account đã tạo
   */
  async create(data: Partial<ZaloAdsAccount>): Promise<ZaloAdsAccount> {
    const adsAccount = this.repository.create(data);
    return this.repository.save(adsAccount);
  }

  /**
   * Cập nhật Ads Account
   * @param id ID của Ads Account
   * @param data Dữ liệu cập nhật
   * @returns Ads Account đã cập nhật
   */
  async update(id: number, data: Partial<ZaloAdsAccount>): Promise<ZaloAdsAccount | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa Ads Account
   * @param id ID của Ads Account
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng Ads Account
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng Ads Account
   */
  async count(options?: FindManyOptions<ZaloAdsAccount>): Promise<number> {
    return this.repository.count(options);
  }
}
