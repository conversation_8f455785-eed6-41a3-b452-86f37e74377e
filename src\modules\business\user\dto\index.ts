export * from './create-product.dto';
export * from './product-response.dto';
export * from './query-product.dto';
export * from './create-physical-product.dto';
export * from './create-digital-product.dto';
export * from './create-event-product.dto';
export * from './create-service-product.dto';
export * from './create-combo-product.dto';

// Export specific items from update DTOs to avoid conflicts
export {
  UpdatePhysicalProductDto,
  ImageOperationDto
} from './update-physical-product.dto';

export { UpdateDigitalProductDto } from './update-digital-product.dto';
export { UpdateEventProductDto } from './update-event-product.dto';
export { UpdateServiceProductDto } from './update-service-product.dto';
export { UpdateComboProductDto } from './update-combo-product.dto';

// Export legacy update DTO
export { BusinessUpdateProductDto } from './update-product.dto';

// Export specific items from old DTOs to avoid conflicts
export {
  DigitalProductDto as LegacyDigitalProductDto,
  DigitalFulfillmentFlowDto as LegacyDigitalFulfillmentFlowDto,
  DigitalOutputDto as LegacyDigitalOutputDto
} from './digital-product.dto';

export {
  EventProductDto as LegacyEventProductDto
} from './event-product.dto';

export {
  ServiceProductDto as LegacyServiceProductDto
} from './service-product.dto';

export {
  ComboProductDto as LegacyComboProductDto,
  ComboItemDto as LegacyComboItemDto
} from './combo-product.dto';

// Export specific items from separate files to avoid conflicts
export { TicketTypeDto as LegacyTicketTypeDtoFromFile } from './ticket-type.dto';
export { ServicePackageDto as LegacyServicePackageDtoFromFile } from './service-package.dto';

export * from './classification.dto';
export * from './custom-field-metadata.dto';
export * from './bulk-delete-product.dto';
export * from './bulk-delete-product-response.dto';
export * from './bulk-delete-custom-field.dto';
export * from './bulk-delete-custom-field-response.dto';
export * from './product-inventory.dto';
export * from './warehouse-list.dto';
export * from './classification.dto';
export * from './custom-field-metadata.dto';
export * from './bulk-delete-product.dto';
export * from './bulk-delete-product-response.dto';
export * from './bulk-delete-custom-field.dto';
export * from './bulk-delete-custom-field-response.dto';
export * from './product-inventory.dto';
export * from './warehouse-list.dto';




export * from './create-custom-field.dto';

// Tránh xung đột tên với product-response.dto
import {
  CustomFieldResponseDto,
} from './custom-field-response.dto';
export {
  CustomFieldResponseDto,
};

export * from './query-custom-field.dto';
export * from './custom-field-list-response.dto';
export * from './custom-field-detail-response.dto';
export * from './update-custom-field.dto';
export * from './component-response.dto';
export * from './create-custom-field-swagger.dto';
export * from './update-custom-field-swagger.dto';

// User Convert
export * from './user-convert-response.dto';
export * from './query-user-convert.dto';

// User Convert Customer
export * from './create-user-convert-customer.dto';
export * from './create-bulk-user-convert-customer.dto';
export * from './bulk-user-convert-customer-response.dto';
export * from './simple-custom-field.dto';
export * from './update-user-convert-customer.dto';
export * from './merge-user-convert-customer.dto';
export * from './user-convert-customer-response.dto';
export * from './query-user-convert-customer.dto';
export * from './bulk-delete-user-convert-customer.dto';
export * from './bulk-delete-user-convert-customer-response.dto';

// Individual update DTOs
export * from './update-customer-basic-info.dto';
export * from './update-customer-custom-fields.dto';
export * from './update-customer-social-links.dto';

export * from './metadata-field.dto';

// Customer Social
export * from './customer-facebook.dto';
export * from './customer-web.dto';
export * from './update-customer-social.dto';
export * from './update-social-links.dto';

// Report
export * from './report';

// User Order
export * from './user-order-response.dto';
export * from './query-user-order.dto';
export * from './user-order-status.dto';
export * from './create-user-order.dto';
export * from './tracking-response.dto';
export * from './print-order-response.dto';
export * from './print-order-query.dto';
export * from './bulk-delete-user-order.dto';
export * from './bulk-delete-user-order-response.dto';

// User Address
export * from './user-address.dto';
export * from './user-shop-info.dto';

// GHTK
export * from './ghtk';

// GHN
export * from './ghn';

// Shipping Management
export * from './track-order.dto';
export * from './cancel-order.dto';
export * from './return-order.dto';
export * from './print-order.dto';
export * from './calculate-shipping-fee.dto';