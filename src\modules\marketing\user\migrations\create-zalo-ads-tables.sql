/*
 * SQL script để tạo các bảng cần thiết cho tích hợp Zalo Ads
 *
 * Script này tạo 3 bảng chính:
 * 1. zalo_ads_accounts: <PERSON><PERSON><PERSON> thông tin về các tài khoản Zalo Ads
 * 2. zalo_ads_campaigns: <PERSON><PERSON><PERSON> thông tin về các chiến dịch quảng cáo Zalo
 * 3. zalo_ads_performance: Lưu thông tin hiệu suất quảng cáo Zalo
 */

-- Tạo bảng zalo_ads_accounts
-- Bảng này lưu trữ thông tin về các tài khoản Zalo Ads mà người dùng đã kết nối với hệ thống
CREATE TABLE zalo_ads_accounts (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,                -- ID người dùng sở hữu tài khoản Ads
  ads_account_id VARCHAR(50) UNIQUE NOT NULL, -- ID của tài khoản Ads trê<PERSON>
  name VARCHAR(255) NOT NULL,              -- Tên của tài khoản Ads
  description TEXT,                        -- <PERSON><PERSON> tả của tài khoản Ads
  access_token VARCHAR(500) NOT NULL,      -- Access token của tài khoản Ads
  refresh_token VARCHAR(500),              -- Refresh token của tài khoản Ads
  expires_at BIGINT NOT NULL,              -- Thời gian hết hạn của access token (Unix timestamp)
  account_status VARCHAR(20) DEFAULT 'active', -- Trạng thái tài khoản (active, inactive, suspended)
  currency VARCHAR(3) DEFAULT 'VND',       -- Đơn vị tiền tệ
  timezone VARCHAR(50) DEFAULT 'Asia/Ho_Chi_Minh', -- Múi giờ
  created_at BIGINT NOT NULL,              -- Thời gian tạo (Unix timestamp)
  updated_at BIGINT NOT NULL               -- Thời gian cập nhật cuối (Unix timestamp)
);

-- Tạo bảng zalo_ads_campaigns
-- Bảng này lưu trữ thông tin về các chiến dịch quảng cáo Zalo
CREATE TABLE zalo_ads_campaigns (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,                -- ID người dùng sở hữu chiến dịch
  ads_account_id VARCHAR(50) NOT NULL,     -- ID của tài khoản Ads
  campaign_id VARCHAR(50) UNIQUE NOT NULL, -- ID của chiến dịch trên Zalo
  name VARCHAR(255) NOT NULL,              -- Tên của chiến dịch
  description TEXT,                        -- Mô tả của chiến dịch
  campaign_status VARCHAR(20) NOT NULL,    -- Trạng thái chiến dịch (active, paused, deleted)
  campaign_objective VARCHAR(50) NOT NULL, -- Mục tiêu chiến dịch (traffic, conversions, brand_awareness)
  budget_type VARCHAR(20) NOT NULL,        -- Loại ngân sách (daily, lifetime)
  daily_budget DECIMAL(15,2),              -- Ngân sách hàng ngày
  lifetime_budget DECIMAL(15,2),           -- Ngân sách tổng
  start_time BIGINT,                       -- Thời gian bắt đầu (Unix timestamp)
  end_time BIGINT,                         -- Thời gian kết thúc (Unix timestamp)
  created_at BIGINT NOT NULL,              -- Thời gian tạo (Unix timestamp)
  updated_at BIGINT NOT NULL               -- Thời gian cập nhật cuối (Unix timestamp)
);

-- Tạo bảng zalo_ads_performance
-- Bảng này lưu trữ thông tin hiệu suất quảng cáo Zalo theo ngày
CREATE TABLE zalo_ads_performance (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,                -- ID người dùng
  ads_account_id VARCHAR(50) NOT NULL,     -- ID của tài khoản Ads
  campaign_id VARCHAR(50) NOT NULL,        -- ID của chiến dịch
  date_start DATE NOT NULL,                -- Ngày bắt đầu báo cáo
  date_end DATE NOT NULL,                  -- Ngày kết thúc báo cáo
  impressions BIGINT DEFAULT 0,            -- Số lượt hiển thị
  clicks BIGINT DEFAULT 0,                 -- Số lượt click
  spend DECIMAL(15,2) DEFAULT 0,           -- Chi phí
  revenue DECIMAL(15,2) DEFAULT 0,         -- Doanh thu
  conversions INTEGER DEFAULT 0,           -- Số lượt chuyển đổi
  ctr DECIMAL(5,2) DEFAULT 0,              -- Tỷ lệ click (%)
  cpc DECIMAL(15,2) DEFAULT 0,             -- Chi phí mỗi click
  cpm DECIMAL(15,2) DEFAULT 0,             -- Chi phí mỗi nghìn lượt hiển thị
  roas DECIMAL(5,2) DEFAULT 0,             -- Return on Ad Spend
  created_at BIGINT NOT NULL,              -- Thời gian tạo (Unix timestamp)
  updated_at BIGINT NOT NULL               -- Thời gian cập nhật cuối (Unix timestamp)
);

-- Tạo các chỉ mục để tối ưu hiệu suất truy vấn

-- Chỉ mục cho bảng zalo_ads_accounts
CREATE INDEX idx_zalo_ads_accounts_user_id ON zalo_ads_accounts(user_id);
CREATE INDEX idx_zalo_ads_accounts_ads_account_id ON zalo_ads_accounts(ads_account_id);
CREATE INDEX idx_zalo_ads_accounts_status ON zalo_ads_accounts(account_status);

-- Chỉ mục cho bảng zalo_ads_campaigns
CREATE INDEX idx_zalo_ads_campaigns_user_id ON zalo_ads_campaigns(user_id);
CREATE INDEX idx_zalo_ads_campaigns_ads_account_id ON zalo_ads_campaigns(ads_account_id);
CREATE INDEX idx_zalo_ads_campaigns_campaign_id ON zalo_ads_campaigns(campaign_id);
CREATE INDEX idx_zalo_ads_campaigns_status ON zalo_ads_campaigns(campaign_status);

-- Chỉ mục cho bảng zalo_ads_performance
CREATE INDEX idx_zalo_ads_performance_user_id ON zalo_ads_performance(user_id);
CREATE INDEX idx_zalo_ads_performance_ads_account_id ON zalo_ads_performance(ads_account_id);
CREATE INDEX idx_zalo_ads_performance_campaign_id ON zalo_ads_performance(campaign_id);
CREATE INDEX idx_zalo_ads_performance_date_range ON zalo_ads_performance(date_start, date_end);
CREATE INDEX idx_zalo_ads_performance_created_at ON zalo_ads_performance(created_at);

-- Tạo chỉ mục kết hợp để tối ưu các truy vấn phức tạp
CREATE INDEX idx_zalo_ads_performance_user_date ON zalo_ads_performance(user_id, date_start, date_end);
CREATE INDEX idx_zalo_ads_performance_account_date ON zalo_ads_performance(ads_account_id, date_start, date_end);

-- Ràng buộc khóa ngoại (nếu cần)
-- Lưu ý: Chỉ thêm nếu bảng users đã tồn tại và có cột id
-- ALTER TABLE zalo_ads_accounts ADD CONSTRAINT fk_zalo_ads_accounts_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
-- ALTER TABLE zalo_ads_campaigns ADD CONSTRAINT fk_zalo_ads_campaigns_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
-- ALTER TABLE zalo_ads_performance ADD CONSTRAINT fk_zalo_ads_performance_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Ràng buộc khóa ngoại giữa các bảng Zalo Ads
ALTER TABLE zalo_ads_campaigns 
ADD CONSTRAINT fk_zalo_ads_campaigns_account 
FOREIGN KEY (ads_account_id) REFERENCES zalo_ads_accounts(ads_account_id) 
ON DELETE CASCADE;

ALTER TABLE zalo_ads_performance 
ADD CONSTRAINT fk_zalo_ads_performance_account 
FOREIGN KEY (ads_account_id) REFERENCES zalo_ads_accounts(ads_account_id) 
ON DELETE CASCADE;

ALTER TABLE zalo_ads_performance 
ADD CONSTRAINT fk_zalo_ads_performance_campaign 
FOREIGN KEY (campaign_id) REFERENCES zalo_ads_campaigns(campaign_id) 
ON DELETE CASCADE;
