import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity cho tài khoản Zalo Ads
 */
@Entity('zalo_ads_accounts')
export class ZaloAdsAccount {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'ads_account_id', unique: true })
  adsAccountId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ name: 'access_token' })
  accessToken: string;

  @Column({ name: 'refresh_token', nullable: true })
  refreshToken?: string;

  @Column({ name: 'expires_at', type: 'bigint' })
  expiresAt: number;

  @Column({ name: 'account_status', default: 'active' })
  accountStatus: string;

  @Column({ name: 'currency', default: 'VND' })
  currency: string;

  @Column({ name: 'timezone', default: 'Asia/Ho_Chi_Minh' })
  timezone: string;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
