import { IsNotEmpty, IsNumber, IsPositive } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * DTO cho việc tạo trường tùy chỉnh
 * Chỉ chứa ID tham chiếu đến định nghĩa trường và giá trị thực tế
 */
export class CreateCustomFieldDto {
  /**
   * ID tham chiếu đến định nghĩa trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID tham chiếu đến định nghĩa trường tùy chỉnh',
    example: 1,
  })
  @Transform(({ value }) => {
    // Chuyển đổi string thành number
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? undefined : parsed;
    }
    return value;
  })
  @IsNotEmpty({ message: 'ID trường không được để trống' })
  @IsNumber({}, { message: 'ID trường phải là số' })
  @IsPositive({ message: 'ID trường phải là số dương' })
  fieldId: number;

  /**
   * Giá trị của trường tùy chỉnh
   * @example "Hà Nội, Việt Nam"
   */
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: 'Hà Nội, Việt Nam',
  })
  @IsNotEmpty({ message: 'Giá trị trường không được để trống' })
  fieldValue: any;
}
