import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserProduct } from '@modules/business/entities';
import { PaginatedResult } from '@common/response/api-response-dto';
import { QueryProductDto, ProductSortField } from '@modules/business/user/dto';
import { SortDirection } from '@common/dto/query.dto';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * Repository xử lý truy vấn dữ liệu cho entity UserProduct
 */
@Injectable()
export class UserProductRepository extends Repository<UserProduct> {
  private readonly logger = new Logger(UserProductRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserProduct, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserProduct
   * @returns SelectQueryBuilder<UserProduct>
   */
  private createBaseQuery(): SelectQueryBuilder<UserProduct> {
    return this.createQueryBuilder('product');
  }

  /**
   * Tìm sản phẩm theo ID
   * @param id ID của sản phẩm
   * @returns Sản phẩm hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<UserProduct | null> {
    try {
      // Lấy cả sản phẩm có status = PENDING hoặc APPROVED với join ProductAdvancedInfo
      return await this.findOne({
        where: [
          { id, status: EntityStatusEnum.PENDING },
          { id, status: EntityStatusEnum.APPROVED }
        ],
        relations: ['advancedInfo']
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm theo ID ${id}: ${error.message}`);
      throw new Error(`Lỗi khi tìm sản phẩm theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm sản phẩm theo ID và ID người dùng
   * @param id ID của sản phẩm
   * @param userId ID của người dùng
   * @returns Sản phẩm hoặc null nếu không tìm thấy
   */
  async findByIdAndUserId(id: number, userId: number): Promise<UserProduct | null> {
    try {
      // Lấy cả sản phẩm có status = PENDING hoặc APPROVED
      return await this.findOne({
        where: [
          { id, createdBy: userId, status: EntityStatusEnum.PENDING },
          { id, createdBy: userId, status: EntityStatusEnum.APPROVED }
        ]
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm theo ID ${id} và userId ${userId}: ${error.message}`);
      throw new Error(`Lỗi khi tìm sản phẩm theo ID ${id} và userId ${userId}: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin created_by của các sản phẩm theo danh sách IDs
   * @param productIds Danh sách ID của sản phẩm
   * @returns Danh sách object chứa id và createdBy
   */
  async findCreatedByForProducts(productIds: number[]): Promise<{ id: number; createdBy: number }[]> {
    try {
      return await this.createBaseQuery()
        .select(['product.id AS id', 'product.created_by AS created_by'])
        .where('product.id IN (:...productIds)', { productIds })
        .andWhere('product.status IN (:...statuses)', {
          statuses: [EntityStatusEnum.PENDING, EntityStatusEnum.APPROVED]
        })
        .getRawMany()
        .then(results =>
          results.map(result => ({
            id: parseInt(result.id),
            createdBy: parseInt(result.created_by)
          }))
        );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy created_by cho products ${productIds.join(',')}: ${error.message}`);
      throw new Error(`Lỗi khi lấy created_by cho products: ${error.message}`);
    }
  }

  /**
   * Tìm nhiều sản phẩm theo danh sách ID
   * @param ids Danh sách ID của sản phẩm
   * @returns Danh sách sản phẩm
   */
  async findByIds(ids: number[]): Promise<UserProduct[]> {
    try {
      if (!ids || ids.length === 0) {
        return [];
      }

      // Sử dụng query builder để tìm sản phẩm theo danh sách ID
      const queryBuilder = this.createBaseQuery();

      queryBuilder
        .where('product.id IN (:...ids)', { ids })
        .andWhere('product.status IN (:...statuses)', {
          statuses: [EntityStatusEnum.PENDING, EntityStatusEnum.APPROVED]
        });

      return await queryBuilder.getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm theo danh sách ID: ${error.message}`);
      throw new Error(`Lỗi khi tìm sản phẩm theo danh sách ID: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm sản phẩm với các điều kiện lọc và phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm với phân trang
   */
  async findProducts(queryDto: QueryProductDto): Promise<PaginatedResult<UserProduct>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        typePrice,
        productType,
        tags,
        sortBy = ProductSortField.CREATED_AT,
        sortDirection = SortDirection.DESC,
        serviceProvider,
        serviceType,
        serviceLocation,
      } = queryDto;

      // Tính toán offset
      const skip = (page - 1) * limit;

      // Tạo query builder
      const queryBuilder = this.createBaseQuery();

      // Lọc các sản phẩm đang hoạt động (PENDING hoặc APPROVED)
      queryBuilder.andWhere('product.status IN (:...statuses)', { statuses: [EntityStatusEnum.PENDING, EntityStatusEnum.APPROVED] });

      // Áp dụng các điều kiện lọc
      if (search) {
        queryBuilder.andWhere('LOWER(product.name) LIKE LOWER(:search)', {
          search: `%${search}%`,
        });
      }

      // Đã loại bỏ điều kiện lọc theo createdBy theo yêu cầu

      if (typePrice) {
        queryBuilder.andWhere('product.typePrice = :typePrice', { typePrice });
      }

      if (productType) {
        queryBuilder.andWhere('product.productType = :productType', { productType });
      }

      if (tags) {
        const tagArray = tags.split(',').map(tag => tag.trim());
        // Sử dụng jsonb_path_exists để tìm kiếm trong mảng tags
        tagArray.forEach((tag, index) => {
          queryBuilder.andWhere(`product.tags @> :tag${index}`, { [`tag${index}`]: [tag] });
        });
      }

      // Service-specific filters (chỉ áp dụng cho sản phẩm SERVICE)
      if (serviceProvider) {
        queryBuilder.andWhere('product.productType = :serviceProductType', { serviceProductType: 'SERVICE' });
        queryBuilder.andWhere('product.metadata->>\'serviceProvider\' ILIKE :serviceProvider', { serviceProvider: `%${serviceProvider}%` });
      }

      if (serviceType) {
        queryBuilder.andWhere('product.productType = :serviceProductType2', { serviceProductType2: 'SERVICE' });
        queryBuilder.andWhere('product.metadata->>\'serviceType\' = :serviceType', { serviceType });
      }

      if (serviceLocation) {
        queryBuilder.andWhere('product.productType = :serviceProductType3', { serviceProductType3: 'SERVICE' });
        queryBuilder.andWhere('product.metadata->>\'serviceLocation\' = :serviceLocation', { serviceLocation });
      }

      // Áp dụng sắp xếp
      queryBuilder.orderBy(`product.${sortBy}`, sortDirection);

      // Áp dụng phân trang
      queryBuilder.skip(skip).take(limit);

      // Thực hiện truy vấn
      const [items, total] = await queryBuilder.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm sản phẩm: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kiếm sản phẩm: ${error.message}`);
    }
  }
}
