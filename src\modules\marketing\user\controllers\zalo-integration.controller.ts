import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ZaloService } from '../services/zalo.service';
import { ZaloAdsOverviewDto } from '../dto/zalo';
import { UserCampaignService } from '../services/user-campaign.service';
import { QueryDto } from '@/common/dto';

/**
 * Controller xử lý API liên quan đến tích hợp Zalo với các kênh khác
 */
@ApiTags(SWAGGER_API_TAGS.ZALO_INTEGRATION)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/integration')
export class ZaloIntegrationController {
  constructor(
    private readonly zaloService: ZaloService,
    private readonly userCampaignService: UserCampaignService,
  ) {}

  /**
   * Lấy danh sách chiến dịch tích hợp
   */
  @Get('campaigns')
  @ApiOperation({ summary: 'Lấy danh sách chiến dịch tích hợp' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách chiến dịch tích hợp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      name: { type: 'string' },
                      description: { type: 'string' },
                      type: { type: 'string' },
                      status: { type: 'string' },
                      channels: { 
                        type: 'array',
                        items: { type: 'string' }
                      },
                      createdAt: { type: 'number' },
                      updatedAt: { type: 'number' },
                    },
                  },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getIntegrationCampaigns(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query() queryDto: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<any>>> {
    const campaigns = await this.userCampaignService.getZaloIntegrationCampaigns(user.id, oaId, queryDto);
    return ApiResponseDto.success(campaigns, 'Lấy danh sách chiến dịch tích hợp thành công');
  }

  /**
   * Lấy thông tin chi tiết chiến dịch tích hợp
   */
  @Get('campaigns/:id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết chiến dịch tích hợp' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết chiến dịch tích hợp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                name: { type: 'string' },
                description: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                channels: { 
                  type: 'array',
                  items: { type: 'string' }
                },
                content: { type: 'object' },
                zaloConfig: { type: 'object' },
                emailConfig: { type: 'object' },
                smsConfig: { type: 'object' },
                createdAt: { type: 'number' },
                updatedAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async getIntegrationCampaignDetail(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<any>> {
    const campaign = await this.userCampaignService.getZaloIntegrationCampaignDetail(user.id, oaId, id);
    return ApiResponseDto.success(campaign, 'Lấy thông tin chi tiết chiến dịch tích hợp thành công');
  }

  /**
   * Tạo chiến dịch tích hợp mới
   */
  @Post('campaigns')
  @ApiOperation({ summary: 'Tạo chiến dịch tích hợp mới' })
  @ApiResponse({
    status: 201,
    description: 'Tạo chiến dịch tích hợp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                name: { type: 'string' },
                description: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                channels: { 
                  type: 'array',
                  items: { type: 'string' }
                },
                createdAt: { type: 'number' },
                updatedAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async createIntegrationCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() createDto: any,
  ): Promise<ApiResponseDto<any>> {
    const campaign = await this.userCampaignService.createZaloIntegrationCampaign(user.id, oaId, createDto);
    return ApiResponseDto.success(campaign, 'Tạo chiến dịch tích hợp thành công');
  }

  /**
   * Cập nhật chiến dịch tích hợp
   */
  @Put('campaigns/:id')
  @ApiOperation({ summary: 'Cập nhật chiến dịch tích hợp' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật chiến dịch tích hợp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                name: { type: 'string' },
                description: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                channels: { 
                  type: 'array',
                  items: { type: 'string' }
                },
                updatedAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async updateIntegrationCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: any,
  ): Promise<ApiResponseDto<any>> {
    const campaign = await this.userCampaignService.updateZaloIntegrationCampaign(user.id, oaId, id, updateDto);
    return ApiResponseDto.success(campaign, 'Cập nhật chiến dịch tích hợp thành công');
  }

  /**
   * Xóa chiến dịch tích hợp
   */
  @Delete('campaigns/:id')
  @ApiOperation({ summary: 'Xóa chiến dịch tích hợp' })
  @ApiResponse({
    status: 200,
    description: 'Xóa chiến dịch tích hợp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' },
          },
        },
      ],
    },
  })
  async deleteIntegrationCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userCampaignService.deleteZaloIntegrationCampaign(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Xóa chiến dịch tích hợp thành công');
  }

  /**
   * Thực thi chiến dịch tích hợp
   */
  @Post('campaigns/:id/execute')
  @ApiOperation({ summary: 'Thực thi chiến dịch tích hợp' })
  @ApiResponse({
    status: 200,
    description: 'Thực thi chiến dịch tích hợp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                status: { type: 'string' },
                startedAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async executeIntegrationCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.userCampaignService.executeZaloIntegrationCampaign(user.id, oaId, id);
    return ApiResponseDto.success(result, 'Thực thi chiến dịch tích hợp thành công');
  }

  /**
   * Lấy lịch sử thực thi chiến dịch tích hợp
   */
  @Get('campaigns/:id/history')
  @ApiOperation({ summary: 'Lấy lịch sử thực thi chiến dịch tích hợp' })
  @ApiResponse({
    status: 200,
    description: 'Lấy lịch sử thực thi chiến dịch tích hợp thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      campaignId: { type: 'number' },
                      channel: { type: 'string' },
                      status: { type: 'string' },
                      totalRecipients: { type: 'number' },
                      successCount: { type: 'number' },
                      failedCount: { type: 'number' },
                      startedAt: { type: 'number' },
                      completedAt: { type: 'number' },
                      createdAt: { type: 'number' },
                    },
                  },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getIntegrationCampaignHistory(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Param('id', ParseIntPipe) id: number,
    @Query() queryDto: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<any>>> {
    const history = await this.userCampaignService.getZaloIntegrationCampaignHistory(user.id, oaId, id, queryDto);
    return ApiResponseDto.success(history, 'Lấy lịch sử thực thi chiến dịch tích hợp thành công');
  }

  /**
   * Đồng bộ người theo dõi Zalo vào danh sách khách hàng
   */
  @Post('sync-followers-to-audience')
  @ApiOperation({ summary: 'Đồng bộ người theo dõi Zalo vào danh sách khách hàng' })
  @ApiResponse({
    status: 200,
    description: 'Đồng bộ người theo dõi Zalo vào danh sách khách hàng thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                totalFollowers: { type: 'number' },
                syncedCount: { type: 'number' },
                audienceId: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async syncFollowersToAudience(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Body() syncDto: { audienceId: number; segmentId?: number; tagName?: string },
  ): Promise<ApiResponseDto<any>> {
    const result = await this.zaloService.syncFollowersToAudience(
      user.id,
      oaId,
      syncDto.audienceId,
      syncDto.segmentId,
      syncDto.tagName,
    );
    return ApiResponseDto.success(result, 'Đồng bộ người theo dõi Zalo vào danh sách khách hàng thành công');
  }

  /**
   * Lấy thống kê tích hợp Zalo
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Lấy thống kê tích hợp Zalo' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê tích hợp Zalo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                totalOfficialAccounts: { type: 'number' },
                totalFollowers: { type: 'number' },
                totalCampaigns: { type: 'number' },
                totalMessagesSent: { type: 'number' },
                totalZnsSent: { type: 'number' },
                interactionRate: { type: 'number' },
                followerGrowth: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      count: { type: 'number' },
                    },
                  },
                },
                messageStats: {
                  type: 'object',
                  properties: {
                    sent: { type: 'number' },
                    delivered: { type: 'number' },
                    read: { type: 'number' },
                    failed: { type: 'number' },
                  },
                },
                znsStats: {
                  type: 'object',
                  properties: {
                    sent: { type: 'number' },
                    delivered: { type: 'number' },
                    read: { type: 'number' },
                    failed: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getIntegrationStatistics(
    @CurrentUser() user: JwtPayload,
    @Param('oaId') oaId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<ApiResponseDto<any>> {
    const statistics = await this.zaloService.getZaloIntegrationStatistics(
      user.id,
      oaId,
      startDate,
      endDate,
    );
    return ApiResponseDto.success(statistics, 'Lấy thống kê tích hợp Zalo thành công');
  }

  /**
   * Lấy thống kê tổng quan Zalo Ads
   */
  @Get('ads/overview')
  @ApiOperation({ summary: 'Lấy thống kê tổng quan Zalo Ads' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê tổng quan Zalo Ads thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                totalAdsAccounts: { type: 'number' },
                totalAdsCampaigns: { type: 'number' },
                totalSpent: { type: 'number' },
                totalRevenue: { type: 'number' },
                totalImpressions: { type: 'number' },
                totalClicks: { type: 'number' },
                roas: { type: 'number' },
                ctr: { type: 'number' },
                avgCpc: { type: 'number' },
                avgCpm: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async getZaloAdsOverview(
    @CurrentUser() user: JwtPayload,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('adsAccountId') adsAccountId?: string,
  ): Promise<ApiResponseDto<ZaloAdsOverviewDto>> {
    const overview = await this.zaloService.getZaloAdsOverview(
      user.id,
      startDate,
      endDate,
      adsAccountId,
    );
    return ApiResponseDto.success(overview, 'Lấy thống kê tổng quan Zalo Ads thành công');
  }
}
