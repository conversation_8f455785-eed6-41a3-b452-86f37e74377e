# Zalo Overview APIs Documentation

## Tổng quan

Tài liệu này mô tả các API để lấy thông tin tổng quan (overview) về Zalo Official Account và Zalo Ads của người dùng.

## 1. API Zalo Official Account Overview

### Endpoint
```
GET /api/marketing/zalo/{oaId}/statistics
```

### Mô tả
Lấy thống kê tích hợp <PERSON>alo Official Account bao gồm:
- Tổng số tài khoản OA của người dùng
- Tổng số followers
- Tổng số chiến dịch
- Tổng số tin nhắn đã gửi
- Tổng số ZNS đã gửi
- Phần trăm tương tác
- Thống kê tăng trưởng followers theo ngày
- Thống kê tin nhắn chi tiết
- Thống kê ZNS chi tiết

### Parameters
- `oaId` (path): ID của Official Account
- `startDate` (query, optional): <PERSON><PERSON><PERSON> b<PERSON> đầ<PERSON> (YYYY-MM-DD)
- `endDate` (query, optional): <PERSON><PERSON><PERSON>ú<PERSON> (YYYY-MM-DD)

### Response
```json
{
  "success": true,
  "message": "Lấy thống kê tích hợp Zalo thành công",
  "data": {
    "totalOfficialAccounts": 3,
    "totalFollowers": 1250,
    "totalCampaigns": 15,
    "totalMessagesSent": 5420,
    "totalZnsSent": 1200,
    "interactionRate": 12.5,
    "followerGrowth": [
      {
        "date": "2024-01-01",
        "count": 25
      },
      {
        "date": "2024-01-02", 
        "count": 30
      }
    ],
    "messageStats": {
      "sent": 5420,
      "delivered": 5380,
      "read": 4200,
      "failed": 40
    },
    "znsStats": {
      "sent": 1200,
      "delivered": 1180,
      "read": 950,
      "failed": 20
    }
  }
}
```

## 2. API Zalo Ads Overview

### Endpoint
```
GET /api/marketing/zalo/ads/overview
```

### Mô tả
Lấy thống kê tổng quan Zalo Ads bao gồm:
- Tổng số tài khoản Zalo Ads
- Tổng số chiến dịch quảng cáo
- Tổng chi phí quảng cáo
- Tổng doanh thu từ quảng cáo
- Chỉ số ROAS (Return on Ad Spend)
- Tổng số lượt hiển thị
- Tổng số lượt click
- Tỷ lệ click (CTR)
- Chi phí trung bình mỗi click (CPC)
- Chi phí trung bình mỗi nghìn lượt hiển thị (CPM)

### Parameters
- `startDate` (query, optional): Ngày bắt đầu (YYYY-MM-DD)
- `endDate` (query, optional): Ngày kết thúc (YYYY-MM-DD)
- `adsAccountId` (query, optional): ID tài khoản Zalo Ads cụ thể

### Response
```json
{
  "success": true,
  "message": "Lấy thống kê tổng quan Zalo Ads thành công",
  "data": {
    "totalAdsAccounts": 2,
    "totalAdsCampaigns": 8,
    "totalSpent": ********,
    "totalRevenue": ********,
    "totalImpressions": 1250000,
    "totalClicks": 12500,
    "roas": 3.5,
    "ctr": 1.0,
    "avgCpc": 1200,
    "avgCpm": 12000
  }
}
```

## 3. Cấu trúc Database

### Bảng Zalo Ads mới được tạo:

#### zalo_ads_accounts
- Lưu thông tin tài khoản Zalo Ads
- Bao gồm access token, thông tin tài khoản

#### zalo_ads_campaigns  
- Lưu thông tin chiến dịch quảng cáo
- Bao gồm ngân sách, mục tiêu, trạng thái

#### zalo_ads_performance
- Lưu dữ liệu hiệu suất theo ngày
- Bao gồm impressions, clicks, spend, revenue, conversions

## 4. Cách sử dụng

### Lấy thống kê OA cho tháng hiện tại:
```bash
curl -X GET "https://api.redai.com/api/marketing/zalo/*********/statistics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Lấy thống kê OA cho khoảng thời gian cụ thể:
```bash
curl -X GET "https://api.redai.com/api/marketing/zalo/*********/statistics?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Lấy tổng quan Zalo Ads:
```bash
curl -X GET "https://api.redai.com/api/marketing/zalo/ads/overview" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Lấy tổng quan Zalo Ads cho tài khoản cụ thể:
```bash
curl -X GET "https://api.redai.com/api/marketing/zalo/ads/overview?adsAccountId=ads_123&startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 5. Lưu ý

1. **Authentication**: Tất cả API đều yêu cầu JWT token hợp lệ
2. **Rate Limiting**: Áp dụng rate limiting để tránh spam
3. **Data Freshness**: Dữ liệu được cập nhật theo thời gian thực từ Zalo API
4. **Error Handling**: API trả về mã lỗi chuẩn HTTP và thông báo lỗi chi tiết
5. **Pagination**: Không áp dụng pagination cho overview APIs vì dữ liệu đã được tổng hợp

## 6. Migration

Để sử dụng các API mới, cần chạy migration SQL:
```bash
psql -d your_database -f src/modules/marketing/user/migrations/create-zalo-ads-tables.sql
```
