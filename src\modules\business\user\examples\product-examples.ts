/**
 * Examples cho API tạo sản phẩm
 */
export const PRODUCT_CREATE_EXAMPLES = {
  physical: {
    summary: 'Sản phẩm vật lý',
    description: '<PERSON><PERSON> dụ tạo sản phẩm vật lý (áo thun)',
    value: {
      "name": "Áo thun nam basic",
      "productType": "PHYSICAL",
      "typePrice": "HAS_PRICE",
      "price": {
        "listPrice": 200000,
        "salePrice": 150000,
        "currency": "VND"
      },
      "description": "Áo thun nam chất liệu cotton cao cấp",
      "imagesMediaTypes": ["image/jpeg", "image/png"],
      "tags": ["áo thun", "nam", "cotton"],
      "customFields": [
        {
          "customFieldId": 5,
          "value": {
            "value": "XL"
          }
        }
      ],
      "shipmentConfig": {
        "widthCm": 25,
        "heightCm": 5,
        "lengthCm": 30,
        "weightGram": 200
      },
      "classifications": [
        {
          "type": "Màu sắc",
          "price": {
            "listPrice": 200000,
            "salePrice": 150000,
            "currency": "VND"
          },
          "imagesMediaTypes": ["image/jpeg"],
          "customFields": [
            {
              "customFieldId": 5,
              "value": {
                "value": "Đỏ"
              }
            }
          ]
        }
      ],
      "inventory": {
        "warehouseId": 1,
        "availableQuantity": 100,
        "sku": "SKU-001",
        "barcode": "1234567890123"
      }
    }
  },
  digital: {
    summary: 'Sản phẩm số',
    description: 'Ví dụ tạo sản phẩm số (khóa học online) với quản lý phiên bản',
    value: {
      name: 'Khóa học lập trình React',
      productType: 'DIGITAL',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 1500000,
        salePrice: 1200000,
        currency: 'VND'
      },
      description: 'Khóa học lập trình React từ cơ bản đến nâng cao',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['khóa học', 'lập trình', 'react', 'online'],
      advancedInfo: {
        purchaseCount: 0,
        digitalFulfillmentFlow: {
          deliveryMethod: 'dashboard_download',
          deliveryTiming: 'immediate',
          deliveryDelayMinutes: 0,
          accessStatus: 'pending'
        },
        digitalOutput: {
          outputType: 'online_course',
          accessLink: 'https://course.example.com/activate?token=abc123',
          loginInfo: {
            username: 'auto_generated',
            password: 'temp_password'
          },
          usageInstructions: 'Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học'
        },
        variantMetadata: {
          variants: [
            {
              name: 'Basic',
              sku: 'BASIC-001',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 1,
              price: {
                listPrice: 500000,
                salePrice: 400000,
                currency: 'VND'
              },
              imagesMediaTypes: ['image/jpeg'],
              description: 'Phiên bản cơ bản - Học React cơ bản'
            },
            {
              name: 'Pro',
              sku: 'PRO-001',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 5,
              price: {
                listPrice: 1500000,
                salePrice: 1200000,
                currency: 'VND'
              },
              imagesMediaTypes: ['image/jpeg', 'image/png'],
              description: 'Phiên bản chuyên nghiệp - Học React từ cơ bản đến nâng cao'
            },
            {
              name: 'Premium',
              sku: 'PREMIUM-001',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 3,
              price: {
                listPrice: 2500000,
                salePrice: 2200000,
                currency: 'VND'
              },
              imagesMediaTypes: ['image/jpeg', 'image/png', 'image/webp'],
              description: 'Phiên bản cao cấp - Học React + NextJS + TypeScript + 1-on-1 mentoring'
            }
          ]
        }
      }
    }
  },
  event: {
    summary: 'Sự kiện',
    description: 'Ví dụ tạo sự kiện (hội thảo) - Giá được lấy từ ticket types. Sự kiện không cần inventory và shipmentConfig.',
    value: {
      name: 'Hội thảo Marketing Digital 2024',
      productType: 'EVENT',
      description: 'Hội thảo về xu hướng Marketing Digital năm 2024',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['hội thảo', 'marketing', 'digital', '2024'],
      customFields: [
        {
          customFieldId: 10,
          value: {
            value: 'Hội thảo'
          }
        },
        {
          customFieldId: 11,
          value: {
            value: 'Trực tiếp + Online'
          }
        },
        {
          customFieldId: 12,
          value: {
            value: '8 giờ'
          }
        }
      ],
      advancedInfo: {
        purchaseCount: 0,
        eventFormat: 'HYBRID',
        eventLink: 'https://zoom.us/j/123456789',
        eventLocation: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
        startDate: 1704067200000,
        endDate: 1704153600000,
        timezone: 'Asia/Ho_Chi_Minh',
        ticketTypes: [
          {
            name: 'Vé thường',
            price: 400000,
            startTime: 1704067200000,
            endTime: 1704153600000,
            timezone: 'Asia/Ho_Chi_Minh',
            description: 'Vé tham gia hội thảo cơ bản',
            quantity: 100,
            minQuantityPerPurchase: 1,
            maxQuantityPerPurchase: 5,
            status: 'PENDING',
            imagesMediaTypes: ['image/jpeg']
          }
        ]
      }
    }
  },
  service: {
    summary: 'Dịch vụ',
    description: 'Ví dụ tạo dịch vụ (tư vấn) với thông tin dịch vụ cơ bản',
    value: {
      name: 'Dịch vụ tư vấn kinh doanh',
      productType: 'SERVICE',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 2000000,
        salePrice: 1800000,
        currency: 'VND'
      },
      description: 'Dịch vụ tư vấn chiến lược kinh doanh cho doanh nghiệp',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['tư vấn', 'kinh doanh', 'chiến lược'],
      // Service-specific fields for frontend compatibility
      serviceTime: 1704067200000,
      serviceDuration: '60',
      serviceProvider: 'Công ty tư vấn ABC',
      serviceType: 'CONSULTATION',
      serviceLocation: 'AT_CENTER'
    }
  },
  serviceAdvanced: {
    summary: 'Dịch vụ nâng cao',
    description: 'Ví dụ tạo dịch vụ nâng cao với servicePackages (advancedInfo)',
    value: {
      name: 'Dịch vụ tư vấn chuyên sâu',
      productType: 'SERVICE',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 3000000,
        salePrice: 2500000,
        currency: 'VND'
      },
      description: 'Dịch vụ tư vấn chuyên sâu với nhiều gói dịch vụ',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['tư vấn', 'chuyên sâu', 'gói dịch vụ'],
      serviceTime: 1704067200000,
      serviceDuration: '120',
      serviceProvider: 'Công ty tư vấn chuyên nghiệp',
      serviceType: 'CONSULTATION',
      serviceLocation: 'HYBRID',
      advancedInfo: {
        purchaseCount: 0,
        servicePackages: [
          {
            name: 'Gói tư vấn cơ bản',
            price: 1800000,
            startTime: 1704067200000,
            endTime: 1704153600000,
            timezone: 'Asia/Ho_Chi_Minh',
            description: 'Gói tư vấn cơ bản bao gồm 3 buổi tư vấn online',
            quantity: 50,
            minQuantityPerPurchase: 1,
            maxQuantityPerPurchase: 3,
            status: 'PENDING',
            imagesMediaTypes: ['image/jpeg']
          }
        ]
      }
    }
  },
  combo: {
    summary: 'Combo sản phẩm',
    description: 'Ví dụ tạo combo sản phẩm - COMBO không cần inventory vì là tập hợp các sản phẩm khác',
    value: {
      name: 'Combo áo thun + quần jean',
      productType: 'COMBO',
      typePrice: 'HAS_PRICE',
      price: {
        listPrice: 800000,
        salePrice: 650000,
        currency: 'VND'
      },
      description: 'Combo áo thun nam + quần jean với giá ưu đãi',
      imagesMediaTypes: ['image/jpeg'],
      tags: ['combo', 'áo thun', 'quần jean', 'ưu đãi'],
      advancedInfo: {
        purchaseCount: 0,
        info: [
          {
            productId: 123,
            total: 2
          },
          {
            productId: 456,
            total: 1
          }
        ]
      }
    }
  }
};

/**
 * Examples cho API cập nhật sản phẩm
 */
export const PRODUCT_UPDATE_EXAMPLES = {
  digital: {
    summary: 'Cập nhật sản phẩm số',
    description: 'Ví dụ cập nhật sản phẩm số (khóa học online) với thao tác hình ảnh cụ thể',
    value: {
      name: 'Khóa học lập trình React - Cập nhật 2024',
      price: {
        listPrice: 1800000,
        salePrice: 1500000,
        currency: 'VND'
      },
      description: 'Khóa học lập trình React từ cơ bản đến nâng cao - Phiên bản cập nhật 2024',
      tags: ['khóa học', 'lập trình', 'react', 'online', '2024'],
      customFields: [
        {
          customFieldId: 15,
          value: {
            value: 'Khóa học trực tuyến'
          }
        },
        {
          customFieldId: 16,
          value: {
            value: 'Có chứng chỉ hoàn thành'
          }
        }
      ],
      imageOperations: [
        {
          operation: 'DELETE',
          key: 'business/IMAGE/2025/06/old-course-image'
        },
        {
          operation: 'ADD',
          mimeType: 'image/jpeg'
        }
      ],
      advancedInfo: {
        purchaseCount: 150,
        digitalFulfillmentFlow: {
          deliveryMethod: 'dashboard_download',
          deliveryTiming: 'immediate',
          deliveryDelayMinutes: 0,
          accessStatus: 'delivered'
        },
        digitalOutput: {
          outputType: 'online_course',
          accessLink: 'https://course.example.com/activate?token=updated123',
          loginInfo: {
            username: 'auto_generated',
            password: 'new_temp_password'
          },
          usageInstructions: 'Vui lòng đăng nhập bằng thông tin mới để truy cập khóa học'
        },
        variantMetadata: {
          variants: [
            {
              name: 'Basic',
              sku: 'BASIC-002',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 1,
              price: {
                listPrice: 600000,
                salePrice: 500000,
                currency: 'VND'
              },
              imageOperations: [
                {
                  operation: 'DELETE',
                  key: 'business/IMAGE/2025/06/old-basic-image'
                },
                {
                  operation: 'ADD',
                  mimeType: 'image/jpeg'
                }
              ],
              description: 'Phiên bản cơ bản đã cập nhật - Học React cơ bản',
              customFields: [
                {
                  customFieldId: 15,
                  value: {
                    value: 'Khóa học React cơ bản'
                  }
                }
              ]
            },
            {
              name: 'Premium',
              sku: 'PREMIUM-001',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 3,
              price: {
                listPrice: 2500000,
                salePrice: 2200000,
                currency: 'VND'
              },
              imageOperations: [
                {
                  operation: 'DELETE',
                  key: 'business/IMAGE/2025/06/old-premium-image'
                },
                {
                  operation: 'ADD',
                  mimeType: 'image/jpeg'
                }
              ],
              description: 'Phiên bản cao cấp mới - Học React + NextJS + TypeScript + 1-on-1 mentoring',
              customFields: [
                {
                  customFieldId: 15,
                  value: {
                    value: 'Khóa học React cao cấp'
                  }
                },
                {
                  customFieldId: 19,
                  value: {
                    value: 'Có mentor 1-on-1'
                  }
                }
              ]
            }
          ]
        }
      }
    }
  },
  physical: {
    summary: 'Cập nhật sản phẩm vật lý',
    description: 'Ví dụ cập nhật sản phẩm vật lý (áo thun) với thay thế toàn bộ hình ảnh (xóa tất cả cũ + thêm tất cả mới)',
    value: {
      name: 'Áo thun nam premium - Cập nhật',
      price: {
        listPrice: 250000,
        salePrice: 200000,
        currency: 'VND'
      },
      description: 'Áo thun nam chất liệu cotton cao cấp - Phiên bản cập nhật',
      tags: ['áo thun', 'nam', 'cotton', 'premium'],
      customFields: [
        {
          customFieldId: 5,
          value: {
            value: 'XL'
          }
        }
      ],
      imageOperations: [
        {
          "operation": "DELETE",
          "key": "business/IMAGE/2025/01/15/old-shirt-image-1.jpg"
        },
        {
          "operation": "ADD",
          "mimeType": "image/jpeg"
        }
        ],
      classifications: [
        {
          type: 'Màu sắc',
          price: {
            listPrice: 250000,
            salePrice: 200000,
            currency: 'VND'
          },
          customFields: [
            {
              customFieldId: 5,
              value: {
                value: 'Đỏ'
              }
            }
          ],
          imageOperations: [
            {
              "operation": "DELETE",
              "key": "business/IMAGE/2025/01/15/old-shirt-image-1.jpg"
            },
            {
              "operation": "ADD",
              "mimeType": "image/jpeg"
            }
          ]
        }
      ],
      shipmentConfig: {
        widthCm: 25,
        heightCm: 5,
        lengthCm: 30,
        weightGram: 220
      },
      inventory: {
        warehouseId: 1,
        availableQuantity: 150,
        sku: 'SHIRT-001-UPDATED',
        barcode: '1234567890124'
      }
    }
  },
  event: {
    summary: 'Cập nhật sự kiện',
    description: 'Ví dụ cập nhật sự kiện (hội thảo) với sửa ảnh (xóa cũ + thêm mới)',
    value: {
      name: 'Hội thảo Marketing Digital 2024 - Cập nhật',
      description: 'Hội thảo về xu hướng Marketing Digital năm 2024 - Phiên bản cập nhật với nhiều nội dung mới',
      tags: ['hội thảo', 'marketing', 'digital', '2024', 'cập nhật'],
      customFields: [
        {
          customFieldId: 10,
          value: {
            value: 'Hội thảo chuyên sâu'
          }
        },
        {
          customFieldId: 11,
          value: {
            value: 'Hybrid - Trực tiếp + Online'
          }
        },
        {
          customFieldId: 12,
          value: {
            value: '10 giờ'
          }
        }
      ],
      imageOperations: [
        {
          operation: 'DELETE',
          key: 'business/IMAGE/2025/01/15/old-event-image.jpg'
        },
        {
          operation: 'ADD',
          mimeType: 'image/jpeg'
        }
      ],
      advancedInfo: {
        purchaseCount: 25,
        eventFormat: 'HYBRID',
        eventLink: 'https://zoom.us/j/987654321',
        eventLocation: 'Trung tâm Hội nghị Quốc gia, Hà Nội - Phòng VIP',
        startDate: 1704067200000,
        endDate: 1704153600000,
        timezone: 'Asia/Ho_Chi_Minh',
        ticketTypes: [
          {
            name: 'Vé VIP',
            price: 600000,
            startTime: 1704067200000,
            endTime: 1704153600000,
            timezone: 'Asia/Ho_Chi_Minh',
            description: 'Vé VIP bao gồm suất ăn và quà tặng',
            quantity: 50,
            minQuantityPerPurchase: 1,
            maxQuantityPerPurchase: 3,
            status: 'PENDING',
            imageOperations: [
              {
                operation: 'DELETE',
                key: 'business/IMAGE/2025/01/15/old-ticket-image.jpg'
              },
              {
                operation: 'ADD',
                mimeType: 'image/jpeg'
              }
            ]
          }
        ]
      }
    }
  },
  service: {
    summary: 'Cập nhật dịch vụ',
    description: 'Ví dụ cập nhật dịch vụ (tư vấn) với thao tác hình ảnh cụ thể (thêm/xóa)',
    value: {
      name: 'Dịch vụ tư vấn kinh doanh - Cập nhật',
      price: {
        listPrice: 2200000,
        salePrice: 2000000,
        currency: 'VND'
      },
      description: 'Dịch vụ tư vấn chiến lược kinh doanh cho doanh nghiệp - Phiên bản cập nhật',
      tags: ['tư vấn', 'kinh doanh', 'chiến lược', 'cập nhật'],
      serviceTime: 1704153600000,
      serviceDuration: '90',
      serviceProvider: 'Công ty tư vấn ABC - Chi nhánh mới',
      serviceType: 'CONSULTATION',
      serviceLocation: 'HYBRID',
      imageOperations: [
        {
          operation: 'DELETE',
          key: 'business/IMAGE/2025/01/15/old-product-image.jpg'
        },
        {
          operation: 'ADD',
          mimeType: 'image/jpeg'
        }
      ],
      advancedInfo: {
        servicePackages: [
          {
            id: 1, // ID của service package cần cập nhật
            name: 'Gói tư vấn cơ bản',
            price: 1500000,
            imageOperations: [
              {
                operation: 'DELETE',
                key: 'business/IMAGE/2025/01/15/old-service-package-image.jpg'
              },
              {
                operation: 'ADD',
                mimeType: 'image/jpeg'
              }
            ]
          }
        ]
      }
    }
  },
  combo: {
    summary: 'Cập nhật combo sản phẩm',
    description: 'Ví dụ cập nhật combo sản phẩm với thêm ảnh mới (chỉ ADD operations)',
    value: {
      name: 'Combo áo thun + quần jean - Cập nhật',
      price: {
        listPrice: 900000,
        salePrice: 750000,
        currency: 'VND'
      },
      description: 'Combo áo thun nam + quần jean với giá ưu đãi - Phiên bản cập nhật',
      tags: ['combo', 'áo thun', 'quần jean', 'ưu đãi', 'cập nhật'],
      imageOperations: [
        {
          operation: 'DELETE',
          key: 'business/IMAGE/2025/06/old-course-image'
        },
        {
          operation: 'ADD',
          mimeType: 'image/jpeg'
        }
      ],
      advancedInfo: {
        purchaseCount: 50,
        combo: {
          info: [
            {
              productId: 123,
              total: 2
            },
            {
              productId: 456,
              total: 1
            },
            {
              productId: 789,
              total: 1
            }
          ]
        }
      }
    }
  }
};
