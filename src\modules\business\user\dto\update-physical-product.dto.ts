import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  Min,
  IsIn,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  PhysicalProductPriceDto,
  PhysicalShipmentConfigDto,
  ProductImagesDto,
  ProductTagsDto,
  ProductMetadataDto
} from './create-physical-product.dto';

/**
 * DTO cho thao tác với hình ảnh (ADD/DELETE)
 */
export class ImageOperationDto {
  @ApiProperty({
    description: 'Loại thao tác',
    enum: ['ADD', 'DELETE'],
    example: 'ADD',
  })
  @IsString()
  @IsIn(['ADD', 'DELETE'])
  operation: 'ADD' | 'DELETE';

  @ApiProperty({
    description: 'URL của hình ảnh',
    example: 'https://cdn.redai.vn/images/product-1.jpg',
  })
  @IsString()
  url: string;

  @ApiProperty({
    description: 'S3 key của hình ảnh',
    example: 'products/user-123/product-456/image-1.jpg',
  })
  @IsString()
  s3Key: string;

  @ApiProperty({
    description: 'Loại MIME của hình ảnh',
    example: 'image/jpeg',
  })
  @IsString()
  mimeType: string;

  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1024000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({
    description: 'Chiều rộng hình ảnh (pixels)',
    example: 800,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({
    description: 'Chiều cao hình ảnh (pixels)',
    example: 600,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    description: 'Có phải là hình ảnh chính không',
    example: true,
    required: false,
  })
  @IsOptional()
  isPrimary?: boolean;
}

/**
 * DTO cho việc cập nhật sản phẩm vật lý
 */
export class UpdatePhysicalProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp - Phiên bản mới',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là PHYSICAL',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum.PHYSICAL;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: PhysicalProductPriceDto,
    required: false,
    example: {
      originalPrice: 150000,
      salePrice: 120000,
      currency: 'VND'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => PhysicalProductPriceDto)
  price?: PhysicalProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton 100%, thoáng mát, phù hợp mọi hoàn cảnh - Phiên bản cải tiến',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
    example: [
      {
        operation: 'DELETE',
        url: 'https://cdn.redai.vn/images/old-shirt-image.jpg',
        s3Key: 'business/IMAGE/2025/06/old-shirt-image',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'ADD',
        url: 'https://cdn.redai.vn/images/new-shirt-image.jpg',
        s3Key: 'business/IMAGE/2025/06/new-shirt-image',
        mimeType: 'image/jpeg',
        isPrimary: true
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  imageOperations?: ImageOperationDto[];

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    type: PhysicalShipmentConfigDto,
    required: false,
    example: {
      widthCm: 30,
      heightCm: 5,
      lengthCm: 35,
      weightGram: 250
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => PhysicalShipmentConfigDto)
  shipmentConfig?: PhysicalShipmentConfigDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;
}
