import { Injectable, Logger } from '@nestjs/common';
import { CustomFieldStatus } from '@modules/business/entities/custom-field.entity';
import { In } from 'typeorm';
import { UserProductAdminRepository } from '@modules/business/repositories';
import { QueryUserProductDto } from '@modules/business/admin/dto';
import { UserProductResponseDto } from '@modules/business/admin/dto';
import { UserProductDetailResponseDto, ClassificationResponseDto, CustomFieldValueWithDetailsResponseDto, CustomGroupFormResponseDto, CustomFieldWithValueResponseDto } from '@modules/business/admin/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { ValidationHelper } from '../helpers/validation.helper';
import {
  CustomFieldRepository,
  UserClassificationRepository,
} from '../../repositories';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { CustomField, UserProduct, UserClassification } from '@modules/business/entities';
import { EntityStatusEnum } from '@modules/business/enums/entity-status.enum';
import { UpdateProductStatusDto } from '../dto/customfields/update-product-status.dto';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý nghiệp vụ liên quan đến sản phẩm của người dùng cho admin
 */
@Injectable()
export class ProductAdminService {
  private readonly logger = new Logger(ProductAdminService.name);

  constructor(
    private readonly userProductAdminRepository: UserProductAdminRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly userClassificationRepository: UserClassificationRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Lấy danh sách sản phẩm với phân trang và lọc
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm phân trang
   * @note Phương thức này chỉ đọc dữ liệu, không cần @Transactional
   */
  async getProducts(
    employeeId: number,
    queryDto: QueryUserProductDto,
  ): Promise<PaginatedResult<UserProductResponseDto>> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy danh sách sản phẩm với query: ${JSON.stringify(queryDto)}`,
    );

    try {
      // Lấy danh sách sản phẩm từ repository
      this.logger.log(`Đang truy vấn danh sách sản phẩm từ database`);
      const productsResult =
        await this.userProductAdminRepository.findProducts(queryDto);
      this.logger.log(`Đã tìm thấy ${productsResult.items.length} sản phẩm`);

      // Chuyển đổi từ entity sang DTO
      this.logger.log(`Đang chuyển đổi dữ liệu sang DTO`);
      const items = productsResult.items.map((product) =>
        this.mapToUserProductResponseDto(product),
      );

      return {
        items,
        meta: productsResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
        error.stack,
        `method: ${this.getProducts.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_FETCH_ERROR,
        'Lỗi khi lấy danh sách sản phẩm',
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm
   * @returns Chi tiết sản phẩm
   * @note Phương thức này chỉ đọc dữ liệu, không cần @Transactional
   */
  async getProductById(
    employeeId: number,
    productId: number,
  ): Promise<UserProductDetailResponseDto> {
    this.logger.log(
      `Nhân viên ${employeeId} đang lấy chi tiết sản phẩm với ID: ${productId}`,
    );

    // Lấy thông tin sản phẩm
    this.logger.log(`Đang truy vấn thông tin sản phẩm với ID: ${productId}`);
    const product =
      await this.userProductAdminRepository.findProductById(productId);
    this.validationHelper.validateProductExists(product);

    // Đảm bảo product không phải là null sau khi validate
    // Vì validateProductExists sẽ throw exception nếu product là null
    const validProduct = product as UserProduct;
    this.logger.log(`Đã tìm thấy sản phẩm: ${validProduct.name}`);

    try {
      // Lấy thông tin nhóm trường tùy chỉnh (không còn liên kết với sản phẩm)
      this.logger.log(
        `Đang truy vấn thông tin nhóm trường tùy chỉnh`,
      );
      const customGroupForms: any[] = []; // Không còn liên kết với sản phẩm
      const customGroupFormIds = customGroupForms.map(cgf => cgf.id);

      // Lấy lên tất cả các trường trong group form đó trong bảng custom group form field
      this.logger.log(`Đang truy vấn tất cả các trường trong các nhóm trường tùy chỉnh`);
      // CustomGroupForm đã bị xóa hoàn toàn
      /*
      const customGroupFormFields = await this.customGroupFormFieldRepository.find({
        where: {
          formGroupId: In(customGroupFormIds)
        }
      });
      */
      const customGroupFormFields = []; // Thay thế bằng mảng rỗng
      this.logger.log(`Đã tìm thấy ${customGroupFormFields.length} trường trong tất cả các nhóm`);

      // Xử lý thông tin nhóm trường tùy chỉnh
      // const customGroupFormDtos = await this.buildCustomGroupFormDto(customGroupForms, customGroupFormFields); // Method đã bị xóa
      const customGroupFormDtos = []; // Thay thế bằng mảng rỗng

      // Lấy thông tin phân loại sản phẩm
      this.logger.log(`Đang truy vấn thông tin phân loại sản phẩm`);
      const classifications = await this.userClassificationRepository.findByProductId_admin(validProduct.id);
      this.logger.log(`Đã tìm thấy ${classifications.length} phân loại sản phẩm`);

      // Xử lý thông tin phân loại
      const classificationDtos = await this.buildClassificationDtos(classifications);

      // Chuyển đổi từ entity sang DTO
      this.logger.log(`Đang chuyển đổi dữ liệu sản phẩm sang DTO`);
      const productDto = this.mapToUserProductResponseDto(validProduct);

      const detailDto: UserProductDetailResponseDto = {
        ...productDto,
        customGroupForms: customGroupFormDtos,
        classifications: classificationDtos.length > 0 ? classificationDtos : null
      };

      this.logger.log(`Hoàn tất lấy chi tiết sản phẩm ID: ${productId}`);
      return detailDto;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
        error.stack,
        `method: ${this.getProductById.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_DETAIL_FETCH_ERROR,
        'Lỗi khi lấy chi tiết sản phẩm',
      );
    }
  }

  /**
   * Cập nhật trạng thái sản phẩm
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateStatusDto DTO chứa thông tin cập nhật trạng thái
   * @returns Số lượng sản phẩm đã cập nhật
   */
  @Transactional() async updateProductStatus(
    employeeId: number,
    updateStatusDto: UpdateProductStatusDto,
  ): Promise<number> {
    const { productIds, status, rejectReason } = updateStatusDto;
    this.logger.log(
      `Nhân viên ${employeeId} đang cập nhật trạng thái ${status} cho ${productIds.length} sản phẩm`,
    );

    try {
      // Kiểm tra lý do từ chối nếu trạng thái mới là REJECTED
      this.validationHelper.validateRejectReason(status, rejectReason);

      // Kiểm tra từng sản phẩm
      for (const productId of productIds) {
        // Kiểm tra sản phẩm tồn tại
        const product =
          await this.userProductAdminRepository.findProductById(productId);
        this.validationHelper.validateProductExists(product);

        // Kiểm tra chuyển đổi trạng thái hợp lệ
        const currentStatus =
          await this.userProductAdminRepository.getProductStatus(productId);
        if (currentStatus) {
          this.validationHelper.validateStatusTransition(currentStatus, status);
        }

        // Nếu đang phê duyệt sản phẩm (chuyển sang APPROVED), kiểm tra cấu trúc giá
        if (status === EntityStatusEnum.APPROVED && product) {
          this.logger.log(`Đang kiểm tra cấu trúc giá của sản phẩm ${productId} trước khi phê duyệt`);
          this.logger.log(`Thông tin sản phẩm - ID: ${product.id}, typePrice: ${product.typePrice}, price: ${JSON.stringify(product.price)}`);
          this.validationHelper.validateProductPrice(product);
        }
      }

      // Cập nhật trạng thái
      const updatedCount =
        await this.userProductAdminRepository.updateProductsStatus(
          productIds,
          status,
        );
      this.logger.log(`Đã cập nhật trạng thái cho ${updatedCount} sản phẩm`);

      // Lưu lý do từ chối nếu có
      if (status === EntityStatusEnum.REJECTED && rejectReason) {
        // TODO: Lưu lý do từ chối vào bảng ghi chú hoặc lịch sử nếu cần
        this.logger.log(`Đã lưu lý do từ chối: ${rejectReason}`);
      }

      return updatedCount;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái sản phẩm: ${error.message}`,
        error.stack,
        `method: ${this.updateProductStatus.name}`
      );
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_STATUS_UPDATE_ERROR,
        'Lỗi khi cập nhật trạng thái sản phẩm',
      );
    }
  }

  /**
   * Xây dựng DTO cho các nhóm trường tùy chỉnh
   * @param customGroupForms Danh sách nhóm trường tùy chỉnh
   * @param customGroupFormFields Danh sách tất cả các trường trong các nhóm
   * @returns Danh sách DTO của các nhóm trường tùy chỉnh
   */
  // buildCustomGroupFormDto đã bị xóa - CustomGroupForm đã bị xóa hoàn toàn
  /*
  private async buildCustomGroupFormDto(
    customGroupForms: CustomGroupForm[],
    customGroupFormFields: CustomGroupFormField[]
  ): Promise<CustomGroupFormResponseDto[]> {
    this.logger.log(`Bắt đầu xây dựng DTO cho nhóm trường tùy chỉnh`, `method: ${this.buildCustomGroupFormDto.name}`);
    if (!customGroupForms || customGroupForms.length === 0) {
      this.logger.log(`Không tìm thấy nhóm trường tùy chỉnh nào cho sản phẩm này`);
      return [];
    }

    this.logger.log(`Đã tìm thấy ${customGroupForms.length} nhóm trường tùy chỉnh`);

    // Nhóm các trường theo formGroupId
    const fieldsByFormGroupId = customGroupFormFields.reduce((acc, field) => {
      if (!acc[field.formGroupId]) {
        acc[field.formGroupId] = [];
      }
      acc[field.formGroupId].push(field);
      return acc;
    }, {} as Record<number, CustomGroupFormField[]>);

    // Lấy tất cả fieldIds để truy vấn chi tiết trường một lần duy nhất
    const allFieldIds = customGroupFormFields.map(field => field.fieldId);
    const uniqueFieldIds = [...new Set(allFieldIds)];

    // Lấy chi tiết tất cả các trường tùy chỉnh trong một lần truy vấn
    this.logger.log(`Đang truy vấn chi tiết ${uniqueFieldIds.length} trường tùy chỉnh`);
    const customFields = uniqueFieldIds.length > 0
      ? await this.customFieldRepository.findByIds(uniqueFieldIds)
      : [];
    this.logger.log(`Đã tìm thấy ${customFields.length} trường tùy chỉnh`);

    // Tạo map để tra cứu nhanh thông tin trường theo ID
    const customFieldsMap = customFields.reduce((map, field) => {
      map[field.id] = field;
      return map;
    }, {} as Record<number, CustomField>);

    // Xử lý từng nhóm trường tùy chỉnh
    const result: CustomGroupFormResponseDto[] = [];

    for (const customGroupForm of customGroupForms) {
      this.logger.log(`Đang xử lý nhóm trường tùy chỉnh: ${customGroupForm.label}`);

      // Lấy danh sách trường trong nhóm từ map đã tạo
      const groupFields = fieldsByFormGroupId[customGroupForm.id] || [];
      this.logger.log(`Đã tìm thấy ${groupFields.length} trường trong nhóm`);

      // Tạo map giá trị trường
      const fieldValuesMap = groupFields.reduce((map, field) => {
        map[field.fieldId] = field.value;
        return map;
      }, {} as Record<number, any>);

      // Lấy chi tiết từng trường
      const fieldIds = groupFields.map((field) => field.fieldId);
      if (fieldIds.length === 0) {
        result.push({
          id: customGroupForm.id,
          label: customGroupForm.label,
          fields: []
        });
        continue;
      }

      result.push({
        id: customGroupForm.id,
        label: customGroupForm.label,
        fields: fieldIds.map(fieldId => {
          const field = customFieldsMap[fieldId];
          if (!field) return null;

          return {
            id: field.id,
            component: field.component,
            configId: field.configId,
            label: field.label,
            type: field.type,
            required: field.required,
            configJson: field.configJson,
            employeeId: field.employeeId,
            userId: field.userId,
            createAt: field.createAt,
            status: field.status as unknown as CustomFieldStatus,
            value: fieldValuesMap[field.id] || null
          };
        }).filter(field => field !== null) as CustomFieldWithValueResponseDto[]
      });
    }

    return result;
  }
  */

  /**
   * Xây dựng DTO cho danh sách phân loại sản phẩm
   * @param classifications Danh sách phân loại sản phẩm
   * @returns Danh sách DTO phân loại sản phẩm
   */
  private async buildClassificationDtos(classifications: UserClassification[]): Promise<ClassificationResponseDto[]> {
    this.logger.log(`Bắt đầu xây dựng DTO cho phân loại sản phẩm`, `method: ${this.buildClassificationDtos.name}`);
    if (classifications.length === 0) {
      return [];
    }

    const classificationIds = classifications.map(c => c.id);
    this.logger.log(`Đang truy vấn giá trị trường tùy chỉnh cho ${classificationIds.length} phân loại`);

    // const customFieldValues = await this.customFieldClassificationRepository.findByClassificationIds(classificationIds); // CustomFieldClassification đã bị xóa
    const customFieldValues = []; // Thay thế bằng mảng rỗng
    this.logger.log(`Đã tìm thấy ${customFieldValues.length} giá trị trường tùy chỉnh cho các phân loại`);

    // Nhóm các giá trị trường tùy chỉnh theo ID phân loại
    const valuesByClassificationId = this.groupCustomFieldValuesByClassificationId(customFieldValues);

    // Lấy thông tin chi tiết của các trường tùy chỉnh
    // const allFieldIds = customFieldValues.map(v => v.customFieldId); // CustomFieldClassification đã bị xóa
    const allFieldIds = []; // Thay thế bằng mảng rỗng
    const uniqueFieldIds = [...new Set(allFieldIds)];

    let fieldDetailsMap = {} as Record<number, CustomField>;
    if (uniqueFieldIds.length > 0) {
      const fieldDetails = await this.customFieldRepository.findByIds(uniqueFieldIds as number[]);

      // Tạo map để tra cứu nhanh thông tin trường theo ID
      fieldDetailsMap = fieldDetails.reduce((map, field) => {
        map[field.id] = field;
        return map;
      }, {} as Record<number, CustomField>);
    }

    // Tạo DTO cho từng phân loại
    const classificationDtos: ClassificationResponseDto[] = [];
    for (const classification of classifications) {
      const customFieldsWithDetails = valuesByClassificationId[classification.id] || [];

      // Thêm thông tin chi tiết cho mỗi trường
      const enhancedCustomFields = this.enhanceCustomFieldsWithDetails(customFieldsWithDetails, fieldDetailsMap);

      classificationDtos.push({
        id: classification.id,
        type: classification.type,
        price: classification.price,
        customFields: enhancedCustomFields.length > 0 ? enhancedCustomFields : null
      });
    }

    return classificationDtos;
  }

  /**
   * Nhóm các giá trị trường tùy chỉnh theo ID phân loại
   * @param customFieldValues Danh sách giá trị trường tùy chỉnh
   * @returns Map chứa các giá trị trường tùy chỉnh theo ID phân loại
   */
  private groupCustomFieldValuesByClassificationId(
    customFieldValues: any[] // CustomFieldClassification đã bị xóa
  ): Record<number, Array<{fieldId: number, value: any}>> {
    this.logger.log(`Nhóm các giá trị trường tùy chỉnh theo ID phân loại`, `method: ${this.groupCustomFieldValuesByClassificationId.name}`);
    return customFieldValues.reduce((acc, curr) => {
      if (!acc[curr.classificationId]) {
        acc[curr.classificationId] = [];
      }
      acc[curr.classificationId].push({
        fieldId: curr.customFieldId,
        value: curr.value
      });
      return acc;
    }, {} as Record<number, Array<{fieldId: number, value: any}>>);
  }

  /**
   * Thêm thông tin chi tiết cho các trường tùy chỉnh
   * @param customFields Danh sách trường tùy chỉnh với giá trị
   * @param fieldDetailsMap Map chứa thông tin chi tiết của các trường
   * @returns Danh sách trường tùy chỉnh với thông tin chi tiết
   */
  private enhanceCustomFieldsWithDetails(
    customFields: Array<{fieldId: number, value: any}>,
    fieldDetailsMap: Record<number, CustomField>
  ): CustomFieldValueWithDetailsResponseDto[] {
    this.logger.log(`Thêm thông tin chi tiết cho các trường tùy chỉnh`, `method: ${this.enhanceCustomFieldsWithDetails.name}`);
    return customFields.map(field => {
      const fieldDetails = fieldDetailsMap[field.fieldId];
      if (!fieldDetails) {
        // Nếu không tìm thấy thông tin chi tiết, bỏ qua trường này
        return null;
      }

      // Trả về cấu trúc giống với fields trong customGroupForm
      return {
        id: fieldDetails.id,
        component: fieldDetails.component,
        configId: fieldDetails.configId,
        label: fieldDetails.label,
        type: fieldDetails.type,
        required: fieldDetails.required,
        configJson: fieldDetails.configJson,
        employeeId: fieldDetails.employeeId,
        userId: fieldDetails.userId,
        createAt: fieldDetails.createAt,
        status: fieldDetails.status, // Thêm trường status
        value: field.value
      };
    }).filter(field => field !== null) as CustomFieldValueWithDetailsResponseDto[];
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param product Entity sản phẩm
   * @returns DTO sản phẩm
   */
  private mapToUserProductResponseDto(
    product: UserProduct,
  ): UserProductResponseDto {
    this.logger.log(`Đang chuyển đổi sản phẩm ${product.id} sang DTO`, `method: ${this.mapToUserProductResponseDto.name}`);


    // Đảm bảo images và tags không bị null
    const images = product.images || [];
    const tags = product.tags || [];

    this.logger.log(
      `Sản phẩm có ${Array.isArray(images) ? images.length : 0} hình ảnh và ${Array.isArray(tags) ? tags.length : 0} tags`,
    );

    // Thêm CDN URL cho hình ảnh
    const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
    const processedImages = Array.isArray(images) ? images.map(img => {
      // Xử lý trường hợp img là object hoặc string
      if (typeof img === 'string') {
        return {
          key: img,
          position: 0,
          url: `${cdnUrl}/${img}`
        };
      } else if (img && typeof img === 'object' && 'key' in img) {
        return {
          ...img,
          url: `${cdnUrl}/${img.key}`
        };
      }
      return img;
    }) : [];

    return {
      id: product.id,
      name: product.name,
      price: product.price ? {
        currency: product.price.currency || 'VND',
        listPrice: product.price.listPrice || product.price.originalPrice || product.price.salePrice || 0,
        salePrice: product.price.salePrice || product.price.originalPrice || 0
      } : {
        currency: 'VND',
        listPrice: 0,
        salePrice: 0
      },
      typePrice: product.typePrice,
      productType: product.productType,
      description: product.description,
      images: processedImages,
      tags: Array.isArray(tags) ? tags : (tags?.items ? tags.items.map(tag => tag.name) : []),
      createdBy: product.createdBy,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      shipmentConfig: product.shipmentConfig,
      status: product.status,
    };
  }
}
