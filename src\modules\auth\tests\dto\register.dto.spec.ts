import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { RegisterDto } from '../../dto/register.dto';

describe('RegisterDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '0912345678',
      countryCode: '+84',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi fullName bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '0912345678',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi email bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      password: 'password123',
      phoneNumber: '0912345678',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi email không đúng định dạng', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: 'invalid-email',
      password: 'password123',
      phoneNumber: '0912345678',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEmail');
  });

  it('phải báo lỗi khi password bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      phoneNumber: '0912345678',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi password ngắn hơn 6 ký tự', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      password: '12345',
      phoneNumber: '0912345678',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('minLength');
  });

  it('phải báo lỗi khi phoneNumber bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      password: 'password123',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi phoneNumber không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: 123456789,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải xác thực DTO hợp lệ với trường ref tùy chọn', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '0912345678',
      countryCode: '+84',
      ref: 12345,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ khi không có trường ref', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '0912345678',
      countryCode: '+84',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi ref không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(RegisterDto, {
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '0912345678',
      ref: 'invalid-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });
});
