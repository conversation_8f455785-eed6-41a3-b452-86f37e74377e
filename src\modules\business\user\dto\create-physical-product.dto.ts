import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  Min,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';

/**
 * DTO cho giá có giá trị cụ thể
 */
export class PhysicalProductPriceDto {
  @ApiProperty({
    description: 'Giá gốc',
    example: 100000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  originalPrice?: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bán',
    example: 80000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  salePrice?: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;
}

/**
 * DTO cho cấu hình vận chuyển sản phẩm vật lý
 */
export class PhysicalShipmentConfigDto {
  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 25,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  widthCm: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  heightCm: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 30,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  lengthCm: number;

  @ApiProperty({
    description: 'Trọng lượng (gram)',
    example: 200,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  weightGram: number;
}

/**
 * DTO cho hình ảnh sản phẩm
 */
export class ProductImageDto {
  @ApiProperty({
    description: 'URL của hình ảnh',
    example: 'https://cdn.redai.vn/images/product-1.jpg',
  })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'S3 key của hình ảnh',
    example: 'products/user-123/product-456/image-1.jpg',
  })
  @IsString()
  @IsNotEmpty()
  s3Key: string;

  @ApiProperty({
    description: 'Loại MIME của hình ảnh',
    example: 'image/jpeg',
  })
  @IsString()
  @IsNotEmpty()
  mimeType: string;

  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1024000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({
    description: 'Chiều rộng hình ảnh (pixels)',
    example: 800,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({
    description: 'Chiều cao hình ảnh (pixels)',
    example: 600,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    description: 'Có phải là hình ảnh chính không',
    example: true,
    required: false,
  })
  @IsOptional()
  isPrimary?: boolean;
}

/**
 * DTO cho danh sách hình ảnh sản phẩm
 */
export class ProductImagesDto {
  @ApiProperty({
    description: 'Danh sách hình ảnh',
    type: [ProductImageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageDto)
  items: ProductImageDto[];
}

/**
 * DTO cho tag sản phẩm
 */
export class ProductTagDto {
  @ApiProperty({
    description: 'ID của tag',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({
    description: 'Tên tag',
    example: 'Thời trang',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Màu sắc tag',
    example: '#FF5733',
    required: false,
  })
  @IsOptional()
  @IsString()
  color?: string;
}

/**
 * DTO cho danh sách tags sản phẩm
 */
export class ProductTagsDto {
  @ApiProperty({
    description: 'Danh sách tags',
    type: [ProductTagDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductTagDto)
  items: ProductTagDto[];
}

/**
 * DTO cho custom field
 */
export class CustomFieldDto {
  @ApiProperty({
    description: 'ID của custom field',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Tên field',
    example: 'Màu sắc',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Loại dữ liệu',
    example: 'text',
  })
  @IsString()
  @IsNotEmpty()
  dataType: string;

  @ApiProperty({
    description: 'Giá trị',
    example: 'Đỏ',
  })
  value: any;

  @ApiProperty({
    description: 'Có bắt buộc không',
    example: false,
    required: false,
  })
  @IsOptional()
  required?: boolean;
}

/**
 * DTO cho metadata sản phẩm
 */
export class ProductMetadataDto {
  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldDto)
  customFields: CustomFieldDto[];
}

/**
 * DTO cho việc tạo sản phẩm vật lý
 */
export class CreatePhysicalProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là PHYSICAL',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @IsEnum(ProductTypeEnum)
  productType: ProductTypeEnum.PHYSICAL;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: PhysicalProductPriceDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PhysicalProductPriceDto)
  price: PhysicalProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton 100%, thoáng mát, phù hợp mọi hoàn cảnh',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  images?: ProductImagesDto;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    type: PhysicalShipmentConfigDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PhysicalShipmentConfigDto)
  shipmentConfig: PhysicalShipmentConfigDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;
}
