import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  Min,
  IsIn,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  ProductImageDto, 
  ProductImagesDto, 
  ProductTagDto, 
  ProductTagsDto, 
  CustomFieldDto, 
  ProductMetadataDto 
} from './create-physical-product.dto';

/**
 * DTO cho giá sản phẩm sự kiện
 */
export class EventProductPriceDto {
  @ApiProperty({
    description: 'Giá gốc',
    example: 500000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  originalPrice?: number;

  @ApiProperty({
    description: 'Giá bán',
    example: 400000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  salePrice?: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;
}

/**
 * DTO cho loại vé sự kiện
 */
export class TicketTypeDto {
  @ApiProperty({
    description: 'ID của ticket type',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({
    description: 'Tên loại vé',
    example: 'Vé VIP',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Mô tả',
    example: 'Vé VIP bao gồm chỗ ngồi hạng nhất và buffet',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Giá vé',
    example: 1000000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({
    description: 'Số lượng vé có sẵn',
    example: 100,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Số lượng vé đã bán',
    example: 25,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  soldQuantity?: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu bán (timestamp)',
    example: 1735689600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  saleStartTime?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc bán (timestamp)',
    example: 1735776000000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  saleEndTime?: number;
}

/**
 * DTO cho việc tạo sản phẩm sự kiện
 */
export class CreateEventProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Hội thảo Marketing Digital 2024',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là EVENT',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.EVENT,
  })
  @IsEnum(ProductTypeEnum)
  productType: ProductTypeEnum.EVENT;

  @ApiProperty({
    description: 'Loại giá (mặc định là HAS_PRICE cho EVENT)',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: EventProductPriceDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => EventProductPriceDto)
  price?: EventProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Hội thảo về xu hướng Marketing Digital mới nhất với các chuyên gia hàng đầu',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  images?: ProductImagesDto;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Hình thức tổ chức sự kiện',
    enum: ['ONLINE', 'OFFLINE', 'HYBRID'],
    example: 'OFFLINE',
  })
  @IsString()
  @IsIn(['ONLINE', 'OFFLINE', 'HYBRID'])
  eventFormat: string;

  @ApiProperty({
    description: 'Đường dẫn tham gia sự kiện (cho sự kiện online)',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLink?: string;

  @ApiProperty({
    description: 'Địa điểm tham gia sự kiện (cho sự kiện offline)',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLocation?: string;

  @ApiProperty({
    description: 'Ngày bắt đầu sự kiện (timestamp)',
    example: 1735689600000,
  })
  @IsNumber()
  startDate: number;

  @ApiProperty({
    description: 'Ngày kết thúc sự kiện (timestamp)',
    example: 1735776000000,
  })
  @IsNumber()
  endDate: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({
    description: 'Danh sách loại vé sự kiện',
    type: [TicketTypeDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketTypeDto)
  ticketTypes?: TicketTypeDto[];

  @ApiProperty({
    description: 'Danh sách hình ảnh cho advanced info',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  advancedImages?: ProductImagesDto;
}
