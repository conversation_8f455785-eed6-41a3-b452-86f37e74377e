import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  ServiceProductPriceDto,
  ServicePackageDto
} from './create-service-product.dto';
import { 
  ProductTagsDto,
  ProductMetadataDto
} from './create-physical-product.dto';
import { ImageOperationDto } from './update-physical-product.dto';

/**
 * DTO cho việc cập nhật sản phẩm dịch vụ
 */
export class UpdateServiceProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Dịch vụ tư vấn Marketing - Gói nâng cao',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là SERVICE',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.SERVICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum.SERVICE;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: ServiceProductPriceDto,
    required: false,
    example: {
      originalPrice: 3000000,
      salePrice: 2500000,
      currency: 'VND'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ServiceProductPriceDto)
  price?: ServiceProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Dịch vụ tư vấn Marketing chuyên nghiệp với đội ngũ chuyên gia giàu kinh nghiệm - Gói nâng cao',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
    example: [
      {
        operation: 'DELETE',
        url: 'https://cdn.redai.vn/images/old-service-image.jpg',
        s3Key: 'business/IMAGE/2025/06/old-service-image',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'ADD',
        url: 'https://cdn.redai.vn/images/new-service-image.jpg',
        s3Key: 'business/IMAGE/2025/06/new-service-image',
        mimeType: 'image/jpeg',
        isPrimary: true
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  imageOperations?: ImageOperationDto[];

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Danh sách gói dịch vụ',
    type: [ServicePackageDto],
    required: false,
    example: [
      {
        name: 'Gói tư vấn cơ bản',
        description: 'Gói tư vấn cơ bản bao gồm phân tích hiện trạng và đưa ra giải pháp',
        price: 5000000,
        durationHours: 40,
        features: ['Phân tích hiện trạng', 'Đưa ra giải pháp', 'Hỗ trợ triển khai'],
        isPopular: false
      },
      {
        name: 'Gói tư vấn nâng cao',
        description: 'Gói tư vấn nâng cao với mentor 1-on-1',
        price: 10000000,
        durationHours: 80,
        features: ['Tất cả tính năng gói cơ bản', 'Mentor 1-on-1', 'Hỗ trợ 24/7'],
        isPopular: true
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ServicePackageDto)
  servicePackages?: ServicePackageDto[];

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh advanced info (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  advancedImageOperations?: ImageOperationDto[];
}
