import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  DigitalProductPriceDto,
  DigitalFulfillmentFlowDto,
  DigitalOutputDto
} from './create-digital-product.dto';
import { 
  ProductTagsDto,
  ProductMetadataDto
} from './create-physical-product.dto';
import { ImageOperationDto } from './update-physical-product.dto';

/**
 * DTO cho việc cập nhật sản phẩm số
 */
export class UpdateDigitalProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Khóa học lập trình Python - Phiên bản nâng cao',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là DIGITAL',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.DIGITAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum.DIGITAL;

  @ApiProperty({
    description: 'Loại giá (hỗ trợ STRING_PRICE)',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: DigitalProductPriceDto,
    required: false,
    example: {
      originalPrice: 2000000,
      salePrice: 1500000,
      currency: 'VND'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalProductPriceDto)
  price?: DigitalProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Khóa học lập trình Python từ cơ bản đến nâng cao với 100+ bài học và dự án thực tế',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
    example: [
      {
        operation: 'DELETE',
        url: 'https://cdn.redai.vn/images/old-course-image.jpg',
        s3Key: 'business/IMAGE/2025/06/old-course-image',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'ADD',
        url: 'https://cdn.redai.vn/images/new-course-image.jpg',
        s3Key: 'business/IMAGE/2025/06/new-course-image',
        mimeType: 'image/jpeg',
        isPrimary: true
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  imageOperations?: ImageOperationDto[];

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
    example: {
      items: [
        { name: 'khóa học', color: '#FF5733' },
        { name: 'lập trình', color: '#33FF57' },
        { name: 'python', color: '#3357FF' },
        { name: 'online', color: '#FF33F5' }
      ]
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Quy trình xử lý đơn hàng số',
    type: DigitalFulfillmentFlowDto,
    required: false,
    example: {
      type: 'AUTOMATIC',
      automaticConfig: {
        sendEmail: true,
        emailTemplate: 'Cảm ơn bạn đã mua khóa học. Link truy cập: {{accessLink}}',
        delaySeconds: 300
      }
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalFulfillmentFlowDto)
  digitalFulfillmentFlow?: DigitalFulfillmentFlowDto;

  @ApiProperty({
    description: 'Đầu ra sản phẩm số',
    type: DigitalOutputDto,
    required: false,
    example: {
      type: 'ACCOUNT_INFO',
      accountInfo: {
        username: 'auto_generated',
        password: 'temp_password_123',
        loginUrl: 'https://course.example.com/login'
      }
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalOutputDto)
  digitalOutput?: DigitalOutputDto;

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh advanced info (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  advancedImageOperations?: ImageOperationDto[];
}
