import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  ComboProductPriceDto,
  ComboInfoDto
} from './create-combo-product.dto';
import { 
  ProductTagsDto,
  ProductMetadataDto
} from './create-physical-product.dto';
import { ImageOperationDto } from './update-physical-product.dto';

/**
 * DTO cho việc cập nhật sản phẩm combo
 */
export class UpdateComboProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Combo thời trang nam mùa hè - Phiên bản đặc biệt',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là COMBO',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.COMBO,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum.COMBO;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: ComboProductPriceDto,
    required: false,
    example: {
      originalPrice: 5000000,
      salePrice: 4000000,
      currency: 'VND'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ComboProductPriceDto)
  price?: ComboProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Combo thời trang nam mùa hè bao gồm áo thun, quần short và phụ kiện - Phiên bản đặc biệt',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
    example: [
      {
        operation: 'DELETE',
        url: 'https://cdn.redai.vn/images/old-combo-image.jpg',
        s3Key: 'business/IMAGE/2025/06/old-combo-image',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'ADD',
        url: 'https://cdn.redai.vn/images/new-combo-image.jpg',
        s3Key: 'business/IMAGE/2025/06/new-combo-image',
        mimeType: 'image/jpeg',
        isPrimary: true
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  imageOperations?: ImageOperationDto[];

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Thông tin combo sản phẩm',
    type: ComboInfoDto,
    required: false,
    example: {
      info: [
        {
          productId: 123,
          productName: 'Áo thun nam',
          total: 2,
          originalPrice: 500000,
          comboPrice: 400000,
          productType: 'PHYSICAL'
        },
        {
          productId: 456,
          productName: 'Quần jean nam',
          total: 1,
          originalPrice: 800000,
          comboPrice: 600000,
          productType: 'PHYSICAL'
        }
      ]
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ComboInfoDto)
  combo?: ComboInfoDto;

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh advanced info (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  advancedImageOperations?: ImageOperationDto[];
}
