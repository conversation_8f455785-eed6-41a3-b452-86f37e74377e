# Hướng dẫn tích hợp Email Marketing Queue

Tài liệu này hướng dẫn cách để app main chính thêm job email marketing vào queue của worker application.

## Tổng quan

Worker application sử dụng **BullMQ** với Redis để xử lý email marketing campaigns bất đồng bộ. Queue `EMAIL_MARKETING` hỗ trợ:

- ✅ Gửi email campaigns với template variables
- ✅ Tracking pixel và click tracking
- ✅ Batch processing với Redis
- ✅ Retry mechanism và error handling
- ✅ Scheduled email delivery

## Cấu trúc Queue

### Queue Configuration
```typescript
// Redis connection
BullModule.forRoot({
  connection: {
    url: env.external.REDIS_URL, // Redis URL từ environment
  },
})

// Email Marketing Queue registration
BullModule.registerQueue({
  name: 'email-marketing',
})
```

## Email Marketing Queue

### Job Data Structure

#### Single Email Job (Legacy)
```typescript
interface EmailMarketingJobDto {
  campaignId: number;           // ID của campaign
  audienceId: number;           // ID của audience nhận email
  email: string;                // Email người nhận
  subject: string;              // Tiêu đề email (có thể chứa {{variable}})
  content: string;              // Nội dung HTML (có thể chứa {{variable}})
  customFields: Record<string, any>; // Dữ liệu để thay thế variables
  server?: {                    // Cấu hình SMTP server (optional)
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };
  trackingId: string;           // ID tracking duy nhất
  createdAt: number;            // Timestamp tạo job
}
```

#### Batch Email Job (Recommended)
```typescript
interface EmailRecipientDto {
  audienceId: number;           // ID của audience nhận email
  email: string;                // Email người nhận
}

interface BatchEmailMarketingJobDto {
  campaignId: number;           // ID của campaign
  templateId: number;           // ID của template email (worker sẽ lấy subject/content)
  templateVariables: Record<string, any>; // Template variables áp dụng cho tất cả recipients
  recipients: EmailRecipientDto[]; // Danh sách người nhận
  server?: {                    // Cấu hình SMTP server (optional)
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };
  createdAt: number;            // Timestamp tạo job
}

// Worker sẽ tự động:
// - Lấy subject/content từ templateId
// - Lấy customFields của từng audience từ database
// - Kết hợp templateVariables (chung) với customFields (riêng từng audience)
// - Tạo tracking ID cho từng email
//
// Cấu trúc dữ liệu:
// - templateVariables: Ở cấp batch job, áp dụng cho tất cả recipients
// - customFields: Worker tự lấy từ database cho từng audienceId
// - recipients: Chỉ cần audienceId và email
```

### Cách thêm job từ App Main

#### Option 1: Sử dụng HTTP API
```bash
# Tạo jobs cho campaign
POST /api/email-marketing/campaigns/{campaignId}/jobs
Content-Type: application/json

# Response
{
  "success": true,
  "message": "Created 3 email marketing jobs",
  "data": {
    "campaignId": 1,
    "jobCount": 3
  }
}
```

#### Option 2: Direct Queue Integration
```typescript
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName } from './queue-name.enum';

@Injectable()
export class MainAppService {
  constructor(
    @InjectQueue(QueueName.EMAIL_MARKETING)
    private emailMarketingQueue: Queue
  ) {}

  // Single email (legacy)
  async sendMarketingEmail(data: EmailMarketingJobDto) {
    const job = await this.emailMarketingQueue.add('send-email', data, {
      delay: 0,                    // Delay trước khi xử lý (ms)
      attempts: 3,                 // Số lần retry
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,       // Giữ lại 100 job completed
      removeOnFail: 50,           // Giữ lại 50 job failed
    });

    return job.id;
  }

  // Batch email (recommended)
  async sendBatchMarketingEmail(data: BatchEmailMarketingJobDto) {
    const job = await this.emailMarketingQueue.add('send-batch-email', data, {
      delay: 0,                    // Delay trước khi xử lý (ms)
      attempts: 3,                 // Số lần retry
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,       // Giữ lại 100 job completed
      removeOnFail: 50,           // Giữ lại 50 job failed
    });

    return job.id;
  }

  // Ví dụ sử dụng batch job
  async createCampaignWithTemplate(templateId: number, recipients: any[], templateVariables: Record<string, any>) {
    const batchJobData: BatchEmailMarketingJobDto = {
      campaignId: 123,
      templateId: templateId,      // Worker sẽ lấy subject/content từ template
      recipients: recipients.map(r => ({
        audienceId: r.id,
        email: r.email,
        customFields: templateVariables  // Template variables cho tất cả
      })),
      server: {
        host: 'smtp.gmail.com',
        port: 587,
        // ...
      },
      createdAt: Date.now()
    };

    return this.sendBatchMarketingEmail(batchJobData);
  }
}
```

### Template Variables
Email content và subject hỗ trợ template variables:
```html
<!-- Email content -->
<h1>Xin chào {{customerName}}!</h1>
<p>Cảm ơn bạn đã mua {{productName}} với giá {{price}}.</p>
<p>Công ty: {{companyName}}</p>
<p>Giảm giá: {{discountPercent}}%</p>

<!-- Subject -->
"{{companyName}} - Đơn hàng {{orderNumber}} đã được xác nhận"
```

### Template Variables vs Custom Fields
1. **templateVariables** (từ API request): Áp dụng cho tất cả recipients
2. **customFields** (từ audience database): Riêng cho từng audience
3. **Merge Priority**: customFields override templateVariables nếu trùng key

```typescript
// Ví dụ: Template có {{companyName}}, {{customerName}}
// API request có templateVariables: { companyName: "RedAI", discountPercent: "50" }
// Audience có customFields: { customerName: "Nguyễn Văn A", phone: "0123456789" }
//
// Worker sẽ merge thành final variables cho audience này:
// {
//   companyName: "RedAI",           // Từ templateVariables
//   discountPercent: "50",          // Từ templateVariables
//   customerName: "Nguyễn Văn A",   // Từ audience customFields (override)
//   phone: "0123456789",            // Từ audience customFields
//   audienceName: "Nguyễn Văn A",   // Default field từ audience
//   audienceEmail: "<EMAIL>" // Default field từ audience
// }

// Job data structure:
{
  campaignId: 123,
  templateId: 456,
  recipients: [
    {
      audienceId: 789,
      email: "<EMAIL>",
      templateVariables: {           // Từ API request
        companyName: "RedAI",
        discountPercent: "50"
      }
    }
  ]
  // Worker sẽ tự lấy customFields của audience 789 từ database
}
```

### Tracking Features
- **Pixel Tracking**: Tự động inject tracking pixel vào email
- **Click Tracking**: Track các link trong email
- **Batch Processing**: Lưu tracking data vào Redis trước, sau đó batch vào DB

## Template Variables System

Email Marketing Queue hỗ trợ template variables để personalize email content:

### Supported Variables
```html
<!-- Email content -->
<h1>Xin chào {{customerName}}!</h1>
<p>Cảm ơn bạn đã mua {{productName}} với giá {{price}}.</p>
<p>Đơn hàng của bạn sẽ được giao vào {{deliveryDate}}.</p>

<!-- Subject -->
"Đơn hàng {{orderNumber}} đã được xác nhận"
```

### Custom Fields Processing
```typescript
// Job data với custom fields
const jobData = {
  campaignId: 1,
  audienceId: 123,
  email: '<EMAIL>',
  subject: 'Xin chào {{customerName}}!',
  content: '<h1>Chào {{customerName}}</h1><p>Sản phẩm {{productName}} đã sẵn sàng!</p>',
  customFields: {
    customerName: 'Nguyễn Văn A',
    productName: 'iPhone 15 Pro',
    price: '25.000.000 VNĐ',
    orderNumber: 'ORD-2024-001',
    deliveryDate: '15/01/2024'
  },
  // ... other fields
};

// Worker sẽ tự động thay thế {{variable}} bằng giá trị trong customFields
```

## Setup cho App Main

### 1. Install Dependencies
```bash
npm install @nestjs/bullmq bullmq ioredis
```

### 2. Module Configuration
```typescript
// app.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';

@Module({
  imports: [
    // Kết nối Redis
    BullModule.forRoot({
      connection: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
      },
    }),

    // Register Email Marketing Queue
    BullModule.registerQueue({
      name: 'email-marketing',
    }),
  ],
})
export class AppModule {}
```

### 3. Email Marketing Service
```typescript
// email-marketing.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

interface EmailMarketingJobDto {
  campaignId: number;
  audienceId: number;
  email: string;
  subject: string;
  content: string;
  customFields: Record<string, any>;
  server?: {
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };
  trackingId: string;
  createdAt: number;
}

@Injectable()
export class EmailMarketingService {
  private readonly logger = new Logger(EmailMarketingService.name);

  constructor(
    @InjectQueue('email-marketing')
    private emailMarketingQueue: Queue,
  ) {}

  /**
   * Gửi email marketing đơn lẻ
   */
  async sendSingleEmail(data: {
    campaignId: number;
    email: string;
    subject: string;
    content: string;
    customFields?: Record<string, any>;
    scheduledAt?: number; // Unix timestamp
    server?: any;
  }): Promise<string> {
    const trackingId = this.generateTrackingId(data.campaignId, Date.now());

    const jobData: EmailMarketingJobDto = {
      campaignId: data.campaignId,
      audienceId: 0, // Single email không có audience ID
      email: data.email,
      subject: data.subject,
      content: data.content,
      customFields: data.customFields || {},
      server: data.server,
      trackingId,
      createdAt: Date.now(),
    };

    const job = await this.emailMarketingQueue.add('send-email', jobData, {
      delay: data.scheduledAt ? Math.max(0, data.scheduledAt - Date.now()) : 0,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    });

    this.logger.log(`Email marketing job created: ${job.id} for ${data.email}`);
    return job.id as string;
  }

  /**
   * Gửi campaign cho nhiều audiences
   */
  async sendCampaign(campaignData: {
    id: number;
    subject: string;
    content: string;
    scheduledAt?: number;
    server?: any;
    audiences: Array<{
      id: number;
      email: string;
      customFields: Record<string, any>;
    }>;
  }): Promise<{ jobIds: string[]; totalJobs: number }> {
    this.logger.log(`Creating jobs for campaign: ${campaignData.id}`);

    const jobs = campaignData.audiences.map(audience => {
      const trackingId = this.generateTrackingId(campaignData.id, audience.id);

      return {
        name: 'send-email',
        data: {
          campaignId: campaignData.id,
          audienceId: audience.id,
          email: audience.email,
          subject: campaignData.subject,
          content: campaignData.content,
          customFields: audience.customFields,
          server: campaignData.server,
          trackingId,
          createdAt: Date.now(),
        } as EmailMarketingJobDto,
        opts: {
          delay: campaignData.scheduledAt ?
            Math.max(0, campaignData.scheduledAt - Date.now()) : 0,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 100,
          removeOnFail: 50,
        }
      };
    });

    // Bulk add jobs
    const addedJobs = await this.emailMarketingQueue.addBulk(jobs);
    const jobIds = addedJobs.map(job => job.id as string);

    this.logger.log(`Created ${jobIds.length} email jobs for campaign ${campaignData.id}`);

    return {
      jobIds,
      totalJobs: jobIds.length,
    };
  }

  /**
   * Kiểm tra trạng thái queue
   */
  async getQueueStatus() {
    const waiting = await this.emailMarketingQueue.getWaiting();
    const active = await this.emailMarketingQueue.getActive();
    const completed = await this.emailMarketingQueue.getCompleted();
    const failed = await this.emailMarketingQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  }

  private generateTrackingId(campaignId: number, audienceId: number): string {
    return `${campaignId}_${audienceId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

## Monitoring & Management

### Queue Dashboard
Worker application có Bull Board dashboard tại:
```
http://localhost:3000/queues
Username: admin
Password: redai@123
```

### Queue Status API
```bash
# Kiểm tra trạng thái queue
GET /api/email-marketing/queue/status

# Response
{
  "success": true,
  "data": {
    "waiting": 10,
    "active": 2,
    "completed": 100,
    "failed": 5,
    "delayed": 0
  }
}
```

## Ví dụ sử dụng thực tế

### 1. Gửi email welcome cho user mới
```typescript
// Trong user registration service
async registerUser(userData: any) {
  // ... logic đăng ký user

  // Gửi email welcome
  await this.emailMarketingService.sendSingleEmail({
    campaignId: 1, // Welcome campaign ID
    email: userData.email,
    subject: 'Chào mừng {{userName}} đến với ứng dụng!',
    content: `
      <h1>Xin chào {{userName}}!</h1>
      <p>Cảm ơn bạn đã đăng ký tài khoản tại ứng dụng của chúng tôi.</p>
      <p>Ngày đăng ký: {{registrationDate}}</p>
      <a href="{{activationLink}}">Kích hoạt tài khoản</a>
    `,
    customFields: {
      userName: userData.name,
      registrationDate: new Date().toLocaleDateString('vi-VN'),
      activationLink: `https://app.com/activate/${userData.activationToken}`,
    },
  });
}
```

### 2. Gửi campaign marketing cho danh sách khách hàng
```typescript
// Trong marketing campaign service
async launchCampaign(campaignId: number) {
  // Lấy thông tin campaign từ database
  const campaign = await this.getCampaign(campaignId);

  // Lấy danh sách audiences
  const audiences = await this.getAudiencesByCampaign(campaignId);

  // Tạo jobs cho campaign
  const result = await this.emailMarketingService.sendCampaign({
    id: campaign.id,
    subject: campaign.subject,
    content: campaign.content,
    scheduledAt: campaign.scheduledAt, // Nếu có lịch gửi
    audiences: audiences.map(audience => ({
      id: audience.id,
      email: audience.email,
      customFields: {
        customerName: audience.name,
        customerType: audience.type,
        lastPurchase: audience.lastPurchaseDate,
        totalSpent: audience.totalSpent,
        // ... other custom fields
      },
    })),
  });

  console.log(`Created ${result.totalJobs} jobs for campaign ${campaignId}`);
  return result;
}
```

### 3. Gửi email theo lịch (scheduled)
```typescript
// Gửi email sau 1 giờ
const oneHourLater = Date.now() + (60 * 60 * 1000);

await this.emailMarketingService.sendSingleEmail({
  campaignId: 2,
  email: '<EMAIL>',
  subject: 'Nhắc nhở: Đơn hàng {{orderNumber}} chưa thanh toán',
  content: `
    <h2>Đơn hàng của bạn đang chờ thanh toán</h2>
    <p>Đơn hàng {{orderNumber}} với tổng giá trị {{totalAmount}} đang chờ thanh toán.</p>
    <a href="{{paymentLink}}">Thanh toán ngay</a>
  `,
  customFields: {
    orderNumber: 'ORD-2024-001',
    totalAmount: '1.500.000 VNĐ',
    paymentLink: 'https://app.com/payment/ORD-2024-001',
  },
  scheduledAt: oneHourLater, // Gửi sau 1 giờ
});
```

## Environment Variables
```bash
# Redis connection (bắt buộc)
REDIS_URL=redis://localhost:6379

# Email configuration (worker sẽ sử dụng)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# App configuration
BASE_URL=https://yourapp.com
```

## Best Practices

### 1. Job Options tối ưu
```typescript
const jobOptions = {
  delay: 0,                    // Delay trước khi xử lý
  attempts: 3,                 // Số lần retry
  backoff: {
    type: 'exponential',       // Exponential backoff
    delay: 2000,               // Delay ban đầu 2s
  },
  removeOnComplete: 100,       // Giữ 100 job completed
  removeOnFail: 50,           // Giữ 50 job failed
  priority: 1,                // Priority (cao hơn = ưu tiên hơn)
};
```

### 2. Bulk Operations cho hiệu suất cao
```typescript
// Thay vì add từng job một
for (const audience of audiences) {
  await queue.add('send-email', jobData); // ❌ Chậm
}

// Sử dụng bulk add
const jobs = audiences.map(audience => ({
  name: 'send-email',
  data: createJobData(audience),
  opts: jobOptions
}));
await queue.addBulk(jobs); // ✅ Nhanh hơn
```

### 3. Error Handling và Monitoring
```typescript
// Listen job events để monitoring
this.emailMarketingQueue.on('completed', (job) => {
  console.log(`✅ Email job ${job.id} completed`);
});

this.emailMarketingQueue.on('failed', (job, err) => {
  console.error(`❌ Email job ${job.id} failed:`, err.message);
  // Có thể gửi alert hoặc log vào monitoring system
});

this.emailMarketingQueue.on('stalled', (job) => {
  console.warn(`⚠️ Email job ${job.id} stalled`);
});
```

## Troubleshooting

### Common Issues

1. **Redis Connection Error**
   ```bash
   # Kiểm tra Redis đang chạy
   redis-cli ping
   # Expected: PONG
   ```

2. **Job không được xử lý**
   - Kiểm tra worker application đang chạy
   - Verify queue name đúng: `'email-marketing'`

3. **Email không được gửi**
   - Kiểm tra SMTP configuration trong worker
   - Verify email credentials

### Debug Commands
```bash
# Xem số lượng jobs trong queue
redis-cli LLEN bull:email-marketing:waiting
redis-cli LLEN bull:email-marketing:active
redis-cli LLEN bull:email-marketing:completed
redis-cli LLEN bull:email-marketing:failed

# Xem chi tiết job (thay JOB_ID)
redis-cli HGETALL bull:email-marketing:JOB_ID

# Clear tất cả jobs (cẩn thận!)
redis-cli FLUSHDB
```
