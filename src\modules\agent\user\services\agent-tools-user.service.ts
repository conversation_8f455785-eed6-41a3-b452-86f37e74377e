import { Injectable, Logger } from '@nestjs/common';
import { AgentUserToolsRepository } from '@modules/agent/repositories/agent-user-tools.repository';
import { AppException } from '@common/exceptions/app.exception';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { PaginatedResult } from '@common/response';
import { UserToolsCustom } from '@modules/tools/entities/user-tools-custom.entity';
import {
  AgentToolsQueryDto,
  AgentToolsResponseDto,
  AddAgentToolsDto,
  RemoveAgentToolsDto,
  BulkOperationResponseDto,
} from '../dto/agent-tools';

/**
 * Service xử lý logic nghiệp vụ cho agent tools
 */
@Injectable()
export class AgentToolsUserService {
  private readonly logger = new Logger(AgentToolsUserService.name);

  constructor(
    private readonly agentUserToolsRepository: AgentUserToolsRepository,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách tools của agent với phân trang
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tools có phân trang
   */
  async getAgentTools(
    agentId: string,
    userId: number,
    queryDto: AgentToolsQueryDto,
  ): Promise<PaginatedResult<AgentToolsResponseDto>> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserToolsRepository.checkAgentExists(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy danh sách tools với phân trang
      const result = await this.agentUserToolsRepository.getAgentToolsWithPagination(
        agentId,
        userId,
        queryDto.page,
        queryDto.limit,
      );

      // Map entities sang DTOs
      const mappedItems: AgentToolsResponseDto[] = result.items.map(this.mapToResponseDto);

      return {
        ...result,
        items: mappedItems,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách tools của agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TOOLS_QUERY_FAILED);
    }
  }

  /**
   * Thêm nhiều tools vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto DTO chứa danh sách tool IDs
   * @returns Kết quả thao tác bulk
   */
  async addToolsToAgent(
    agentId: string,
    userId: number,
    addDto: AddAgentToolsDto,
  ): Promise<BulkOperationResponseDto> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserToolsRepository.checkAgentExists(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Validate tools existence và thuộc về user
      const validToolIds = await this.agentUserToolsRepository.validateToolsExistence(
        addDto.toolIds,
        userId,
      );

      if (validToolIds.length === 0) {
        throw new AppException(AGENT_ERROR_CODES.TOOLS_NOT_FOUND);
      }

      // Tìm tools không hợp lệ
      const invalidToolIds = addDto.toolIds.filter(id => !validToolIds.includes(id));

      // Bulk add tools
      const result = await this.agentUserToolsRepository.bulkAddTools(agentId, validToolIds);

      this.logger.log(
        `User ${userId} added ${result.addedCount} tools to agent ${agentId}, ` +
        `${result.skippedCount} skipped, ${invalidToolIds.length} invalid`
      );

      return {
        processedCount: result.addedCount,
        skippedCount: result.skippedCount + invalidToolIds.length,
        skippedIds: [...result.existingIds, ...invalidToolIds],
        totalRequested: addDto.toolIds.length,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi thêm tools vào agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TOOLS_ADD_FAILED);
    }
  }

  /**
   * Gỡ bỏ nhiều tools khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto DTO chứa danh sách tool IDs
   * @returns Kết quả thao tác bulk
   */
  async removeToolsFromAgent(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentToolsDto,
  ): Promise<BulkOperationResponseDto> {
    try {
      // Kiểm tra agent có tồn tại và thuộc về user không
      const agentExists = await this.agentUserToolsRepository.checkAgentExists(agentId, userId);
      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Validate tools existence và thuộc về user
      const validToolIds = await this.agentUserToolsRepository.validateToolsExistence(
        removeDto.toolIds,
        userId,
      );

      // Tìm tools không hợp lệ
      const invalidToolIds = removeDto.toolIds.filter(id => !validToolIds.includes(id));

      // Bulk remove tools (chỉ remove những tools hợp lệ)
      const removedCount = await this.agentUserToolsRepository.bulkRemoveTools(
        agentId,
        validToolIds,
      );

      this.logger.log(
        `User ${userId} removed ${removedCount} tools from agent ${agentId}, ` +
        `${invalidToolIds.length} invalid tools skipped`
      );

      return {
        processedCount: removedCount,
        skippedCount: invalidToolIds.length,
        skippedIds: invalidToolIds,
        totalRequested: removeDto.toolIds.length,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ tools khỏi agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_TOOLS_REMOVE_FAILED);
    }
  }

  /**
   * Map UserToolsCustom entity sang AgentToolsResponseDto
   * @param entity UserToolsCustom entity
   * @returns AgentToolsResponseDto
   */
  private mapToResponseDto(entity: UserToolsCustom): AgentToolsResponseDto {
    return {
      id: entity.id,
      toolName: entity.toolName,
      toolDescription: entity.toolDescription,
      endpoint: entity.endpoint,
      method: entity.method,
      baseUrl: entity.baseUrl,
      status: entity.status,
      active: entity.active,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }
}
