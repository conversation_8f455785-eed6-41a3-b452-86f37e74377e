import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin tổng quan Zalo Ads
 */
export class ZaloAdsOverviewDto {
  @ApiProperty({
    description: 'Tổng số tài khoản Zalo Ads',
    example: 5,
  })
  totalAdsAccounts: number;

  @ApiProperty({
    description: 'Tổng số chiến dịch quảng cáo',
    example: 25,
  })
  totalAdsCampaigns: number;

  @ApiProperty({
    description: 'Tổng chi phí quảng cáo (VND)',
    example: ********,
  })
  totalSpent: number;

  @ApiProperty({
    description: 'Chỉ số ROAS (Return on Ad Spend)',
    example: 3.5,
  })
  roas: number;

  @ApiProperty({
    description: 'Tổng doanh thu từ quảng cáo (VND)',
    example: ********,
  })
  totalRevenue: number;

  @ApiProperty({
    description: 'Tổng số lượt hiển thị',
    example: 1250000,
  })
  totalImpressions: number;

  @ApiProperty({
    description: 'Tổng số lượt click',
    example: 12500,
  })
  totalClicks: number;

  @ApiProperty({
    description: 'Tỷ lệ click (CTR) - %',
    example: 1.0,
  })
  ctr: number;

  @ApiProperty({
    description: 'Chi phí trung bình mỗi click (CPC) - VND',
    example: 1200,
  })
  avgCpc: number;

  @ApiProperty({
    description: 'Chi phí trung bình mỗi nghìn lượt hiển thị (CPM) - VND',
    example: 12000,
  })
  avgCpm: number;
}

/**
 * DTO cho query parameters của Zalo Ads overview
 */
export class ZaloAdsOverviewQueryDto {
  @ApiProperty({
    description: 'Ngày bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
    required: false,
  })
  startDate?: string;

  @ApiProperty({
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2024-01-31',
    required: false,
  })
  endDate?: string;

  @ApiProperty({
    description: 'ID tài khoản Zalo Ads cụ thể (tùy chọn)',
    example: 'ads_account_123',
    required: false,
  })
  adsAccountId?: string;
}
