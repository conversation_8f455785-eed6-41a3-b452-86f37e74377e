import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  IsObject,
  ValidateNested,
  IsArray,
  IsEnum
} from 'class-validator';
import { CustomFieldInputDto } from './custom-field-metadata.dto';

/**
 * DTO cho quy trình xử lý đơn hàng số (Digital Fulfillment Flow)
 */
export class DigitalFulfillmentFlowDto {
  @ApiProperty({
    description: 'Cách giao hàng số',
    example: 'Gửi qua email',
    enum: ['email', 'dashboard_download', 'course_activation', 'license_key'],
  })
  @IsString()
  @IsNotEmpty()
  deliveryMethod: string;

  @ApiProperty({
    description: 'Thời điểm giao hàng',
    example: 'Sau khi thanh toán thành công',
    enum: ['immediate', 'after_payment', 'scheduled'],
  })
  @IsString()
  @IsNotEmpty()
  deliveryTiming: string;

  @ApiProperty({
    description: 'Thời gian chờ giao hàng (phút)',
    example: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  deliveryDelayMinutes?: number;

  @ApiProperty({
    description: 'Tình trạng truy cập',
    example: 'Đã giao',
    enum: ['delivered', 'pending', 'failed'],
  })
  @IsString()
  @IsNotEmpty()
  accessStatus: string;
}

/**
 * DTO cho đầu ra sản phẩm số (Digital Output/Access)
 */
export class DigitalOutputDto {
  @ApiProperty({
    description: 'Loại sản phẩm số',
    example: 'Khóa học online',
    enum: ['online_course', 'downloadable_file', 'license_key', 'ebook'],
  })
  @IsString()
  @IsNotEmpty()
  outputType: string;

  @ApiProperty({
    description: 'Đường link kích hoạt hoặc truy cập',
    example: 'https://course.example.com/activate?token=abc123',
    required: false,
  })
  @IsOptional()
  @IsString()
  accessLink?: string;

  @ApiProperty({
    description: 'Thông tin đăng nhập (JSON)',
    example: { username: 'user123', password: 'temp_password' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  loginInfo?: any;

  @ApiProperty({
    description: 'Link tải file (có thời hạn hoặc không)',
    example: 'https://cdn.example.com/download/file123.pdf?expires=**********',
    required: false,
  })
  @IsOptional()
  @IsString()
  downloadLink?: string;

  @ApiProperty({
    description: 'Key bản quyền',
    example: 'ABCD-EFGH-IJKL-MNOP',
    required: false,
  })
  @IsOptional()
  @IsString()
  licenseKey?: string;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'Vui lòng nhập key bản quyền vào phần mềm để kích hoạt',
    required: false,
  })
  @IsOptional()
  @IsString()
  usageInstructions?: string;
}

/**
 * DTO cho giá phiên bản sản phẩm số
 */
export class DigitalVariantPriceDto {
  @ApiProperty({
    description: 'Giá niêm yết',
    example: 1500000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  listPrice: number;

  @ApiProperty({
    description: 'Giá bán',
    example: 1200000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  salePrice: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    enum: ['VND', 'USD', 'EUR'],
  })
  @IsString()
  @IsNotEmpty()
  currency: string;
}

/**
 * DTO cho phiên bản sản phẩm số
 */
export class DigitalVariantDto {
  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'Basic, Pro, Premium...',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Mã SKU của phiên bản',
    example: 'BASIC-001',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  sku: string;

  @ApiProperty({
    description: 'Số lượng có sẵn',
    example: 1,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  availableQuantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu mỗi lần mua',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  minQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Số lượng tối đa mỗi lần mua',
    example: 10,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  maxQuantityPerPurchase: number;

  @ApiProperty({
    description: 'Giá của phiên bản',
    type: DigitalVariantPriceDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalVariantPriceDto)
  price: DigitalVariantPriceDto;

  @ApiProperty({
    description: 'Danh sách loại media của hình ảnh phiên bản (deprecated - sử dụng images thay thế)',
    example: ['image/jpeg', 'image/png'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];

  @ApiProperty({
    description: 'Danh sách thao tác ảnh cho phiên bản',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        operation: { type: 'string', enum: ['ADD', 'DELETE'], example: 'ADD' },
        position: { type: 'number', example: 1, description: 'Vị trí ảnh cần xóa (cho DELETE)' },
        key: { type: 'string', example: 'uploads/user_products/2025/05/variant-image.jpg', description: 'Khóa tệp (cho DELETE)' },
        mimeType: { type: 'string', example: 'image/png', description: 'Loại MIME (cho ADD)' }
      }
    },
    example: [
      {
        operation: 'ADD',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'DELETE',
        position: 0
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  images?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;

  @ApiProperty({
    description: 'Danh sách thao tác ảnh cho phiên bản (tên field thay thế cho images để nhất quán với API chính)',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        operation: { type: 'string', enum: ['ADD', 'DELETE'], example: 'ADD' },
        position: { type: 'number', example: 1, description: 'Vị trí ảnh cần xóa (cho DELETE)' },
        key: { type: 'string', example: 'uploads/user_products/2025/05/variant-image.jpg', description: 'Khóa tệp (cho DELETE)' },
        mimeType: { type: 'string', example: 'image/png', description: 'Loại MIME (cho ADD)' }
      }
    },
    example: [
      {
        operation: 'ADD',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'DELETE',
        key: 'business/IMAGE/2025/06/old-variant-image'
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  imageOperations?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;

  @ApiProperty({
    description: 'Mô tả chi tiết về phiên bản này',
    example: 'Mô tả chi tiết về phiên bản này...',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách custom fields cho phiên bản này',
    type: [CustomFieldInputDto],
    required: false,
    example: [
      {
        customFieldId: 15,
        value: {
          value: 'Khóa học trực tuyến'
        }
      },
      {
        customFieldId: 16,
        value: {
          value: 'Có chứng chỉ hoàn thành'
        }
      }
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];
}

/**
 * DTO cho metadata quản lý phiên bản sản phẩm số
 */
export class DigitalVariantMetadataDto {
  @ApiProperty({
    description: 'Danh sách phiên bản sản phẩm số',
    type: [DigitalVariantDto],
    example: [
      {
        name: 'Basic',
        sku: 'BASIC-001',
        availableQuantity: 1,
        minQuantityPerPurchase: 1,
        maxQuantityPerPurchase: 1,
        price: {
          listPrice: 500000,
          salePrice: 400000,
          currency: 'VND'
        },
        images: [
          {
            operation: 'ADD',
            mimeType: 'image/jpeg'
          }
        ],
        description: 'Phiên bản cơ bản',
        customFields: [
          {
            customFieldId: 15,
            value: {
              value: 'Khóa học cơ bản'
            }
          }
        ]
      },
      {
        name: 'Pro',
        sku: 'PRO-001',
        availableQuantity: 1,
        minQuantityPerPurchase: 1,
        maxQuantityPerPurchase: 5,
        price: {
          listPrice: 1500000,
          salePrice: 1200000,
          currency: 'VND'
        },
        images: [
          {
            operation: 'DELETE',
            position: 0
          },
          {
            operation: 'ADD',
            mimeType: 'image/jpeg'
          },
          {
            operation: 'ADD',
            mimeType: 'image/png'
          }
        ],
        description: 'Phiên bản chuyên nghiệp',
        customFields: [
          {
            customFieldId: 15,
            value: {
              value: 'Khóa học chuyên nghiệp'
            }
          },
          {
            customFieldId: 16,
            value: {
              value: 'Có mentor 1-on-1'
            }
          }
        ]
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DigitalVariantDto)
  variants?: DigitalVariantDto[];
}

/**
 * DTO cho thông tin nâng cao của sản phẩm số
 */
export class DigitalProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 150,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  purchaseCount: number;

  @ApiProperty({
    description: 'Quy trình xử lý đơn hàng số',
    type: DigitalFulfillmentFlowDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalFulfillmentFlowDto)
  digitalFulfillmentFlow: DigitalFulfillmentFlowDto;

  @ApiProperty({
    description: 'Đầu ra sản phẩm số',
    type: DigitalOutputDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalOutputDto)
  digitalOutput: DigitalOutputDto;

  @ApiProperty({
    description: 'Metadata quản lý phiên bản sản phẩm số (lưu trong trường metadata của product)',
    type: DigitalVariantMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalVariantMetadataDto)
  variantMetadata?: DigitalVariantMetadataDto;
}
