import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity cho chiến dịch <PERSON>alo <PERSON>s
 */
@Entity('zalo_ads_campaigns')
export class ZaloAdsCampaign {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'ads_account_id' })
  adsAccountId: string;

  @Column({ name: 'campaign_id', unique: true })
  campaignId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ name: 'campaign_status' })
  campaignStatus: string;

  @Column({ name: 'campaign_objective' })
  campaignObjective: string;

  @Column({ name: 'budget_type' })
  budgetType: string;

  @Column({ name: 'daily_budget', type: 'decimal', precision: 15, scale: 2, nullable: true })
  dailyBudget?: number;

  @Column({ name: 'lifetime_budget', type: 'decimal', precision: 15, scale: 2, nullable: true })
  lifetimeBudget?: number;

  @Column({ name: 'start_time', type: 'bigint', nullable: true })
  startTime?: number;

  @Column({ name: 'end_time', type: 'bigint', nullable: true })
  endTime?: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
