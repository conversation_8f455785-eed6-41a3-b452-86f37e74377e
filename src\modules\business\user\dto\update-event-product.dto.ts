import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  IsIn,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  EventProductPriceDto,
  TicketTypeDto
} from './create-event-product.dto';
import { 
  ProductTagsDto,
  ProductMetadataDto
} from './create-physical-product.dto';
import { ImageOperationDto } from './update-physical-product.dto';

/**
 * DTO cho việc cập nhật sản phẩm sự kiện
 */
export class UpdateEventProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Hội thảo Marketing Digital 2024 - Phiên bản mở rộng',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là EVENT',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.EVENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  productType?: ProductTypeEnum.EVENT;

  @ApiProperty({
    description: 'Loại giá (mặc định là HAS_PRICE cho EVENT)',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: EventProductPriceDto,
    required: false,
    example: {
      originalPrice: 800000,
      salePrice: 600000,
      currency: 'VND'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => EventProductPriceDto)
  price?: EventProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Hội thảo về xu hướng Marketing Digital mới nhất với các chuyên gia hàng đầu - Phiên bản mở rộng',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
    example: [
      {
        operation: 'DELETE',
        url: 'https://cdn.redai.vn/images/old-event-banner.jpg',
        s3Key: 'business/IMAGE/2025/06/old-event-banner',
        mimeType: 'image/jpeg'
      },
      {
        operation: 'ADD',
        url: 'https://cdn.redai.vn/images/new-event-banner.jpg',
        s3Key: 'business/IMAGE/2025/06/new-event-banner',
        mimeType: 'image/jpeg',
        isPrimary: true
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  imageOperations?: ImageOperationDto[];

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Hình thức tổ chức sự kiện',
    enum: ['ONLINE', 'OFFLINE', 'HYBRID'],
    example: 'OFFLINE',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['ONLINE', 'OFFLINE', 'HYBRID'])
  eventFormat?: string;

  @ApiProperty({
    description: 'Đường dẫn tham gia sự kiện (cho sự kiện online)',
    example: 'https://zoom.us/j/123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLink?: string;

  @ApiProperty({
    description: 'Địa điểm tham gia sự kiện (cho sự kiện offline)',
    example: 'Trung tâm Hội nghị Quốc gia, Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  eventLocation?: string;

  @ApiProperty({
    description: 'Ngày bắt đầu sự kiện (timestamp)',
    example: 1735689600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  startDate?: number;

  @ApiProperty({
    description: 'Ngày kết thúc sự kiện (timestamp)',
    example: 1735776000000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  endDate?: number;

  @ApiProperty({
    description: 'Múi giờ',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({
    description: 'Danh sách loại vé sự kiện',
    type: [TicketTypeDto],
    required: false,
    example: [
      {
        name: 'Vé VIP',
        description: 'Vé VIP bao gồm chỗ ngồi hạng nhất và buffet',
        price: 1200000,
        quantity: 50,
        soldQuantity: 15
      },
      {
        name: 'Vé thường',
        description: 'Vé tham dự sự kiện',
        price: 500000,
        quantity: 200,
        soldQuantity: 80
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketTypeDto)
  ticketTypes?: TicketTypeDto[];

  @ApiProperty({
    description: 'Danh sách thao tác với hình ảnh advanced info (ADD/DELETE)',
    type: [ImageOperationDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageOperationDto)
  advancedImageOperations?: ImageOperationDto[];
}
