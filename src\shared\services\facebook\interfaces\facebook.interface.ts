/**
 * Interface cho Facebook Page
 */
export interface FacebookPageInfo {
  id: string;
  name: string;
  access_token: string;
  category?: string;
  tasks?: string[];
  picture?: {
    data: {
      url: string;
      is_silhouette: boolean;
    };
  };
}

/**
 * Interface cho response của Facebook Picture API khi redirect=false
 */
export interface FacebookPictureResponse {
  data: {
    /**
     * Chiều cao của ảnh
     */
    height: number;

    /**
     * Chiều rộng của ảnh
     */
    width: number;

    /**
     * Cờ đánh dấu ảnh là silhouette (ảnh mặc định) hay không
     */
    is_silhouette: boolean;

    /**
     * URL của ảnh
     */
    url: string;
  };
}

/**
 * Interface cho Facebook User
 */
export interface FacebookUserInfo {
  id: string;
  name?: string;
  email?: string;
  picture?: {
    data: {
      url: string;
      is_silhouette: boolean;
    };
  };
}

/**
 * Interface cho Facebook Auth Response
 */
export interface FacebookAuthResponse {
  access_token: string;
  token_type: string;
  expires_in?: number; // <PERSON><PERSON> thể undefined với một số loại token
}

/**
 * Interface cho Facebook Message Response
 */
export interface FacebookMessageResponse {
  message_id: string;
  recipient_id?: string;
}

/**
 * Interface cho Facebook Error
 */
export interface FacebookError {
  message: string;
  type: string;
  code: number;
  error_subcode?: number;
  fbtrace_id: string;
}

/**
 * Interface cho Facebook API Response
 */
export interface FacebookApiResponse<T> {
  data?: T;
  error?: FacebookError;
}

/**
 * Interface cho Facebook Webhook Event
 */
export interface FacebookWebhookEvent {
  object: string;
  entry: Array<{
    id: string;
    time: number;
    messaging?: Array<{
      sender: {
        id: string;
      };
      recipient: {
        id: string;
      };
      timestamp: number;
      message?: {
        mid: string;
        text: string;
        quick_reply?: {
          payload: string;
        };
        attachments?: Array<{
          type: string;
          payload: {
            url?: string;
            sticker_id?: number;
            coordinates?: {
              lat: number;
              long: number;
            };
          };
        }>;
      };
      postback?: {
        title: string;
        payload: string;
      };
    }>;
  }>;
}

/**
 * Interface cho Facebook Webhook Verification
 */
export interface FacebookWebhookVerification {
  'hub.mode': string;
  'hub.verify_token': string;
  'hub.challenge': string;
}

/**
 * Interface cho Facebook Long-Lived Token Response
 */
export interface FacebookLongLivedTokenResponse {
  access_token: string;
  token_type: string;
  expires_in?: number; // Có thể undefined với long-lived token
  app_id?: string;
  application?: string;
  data_access_expires_at?: number;
  is_valid?: boolean;
  issued_at?: number;
  profile_id?: string;
  scopes?: string[];
  user_id?: string;
}
