import { ApiProperty } from '@nestjs/swagger';

/**
 * D<PERSON> cho kết quả từng item trong bulk delete đơn hàng
 */
export class BulkDeleteUserOrderResultItemDto {
  /**
   * ID đơn hàng
   * @example 1
   */
  @ApiProperty({
    description: 'ID đơn hàng',
    example: 1,
  })
  orderId: number;

  /**
   * Trạng thái xử lý
   * - success: Xóa thành công
   * - error: Có lỗi xảy ra
   * @example "success"
   */
  @ApiProperty({
    description: 'Trạng thái xử lý',
    enum: ['success', 'error'],
    example: 'success',
  })
  status: 'success' | 'error';

  /**
   * Thông báo chi tiết
   * @example "Xóa đơn hàng thành công"
   */
  @ApiProperty({
    description: 'Thông báo chi tiết',
    example: 'Xóa đơn hàng thành công',
  })
  message: string;
}

/**
 * D<PERSON> cho phản hồi bulk delete đơn hàng
 */
export class BulkDeleteUserOrderResponseDto {
  /**
   * Tổng số đơn hàng được yêu cầu xóa
   * @example 5
   */
  @ApiProperty({
    description: 'Tổng số đơn hàng được yêu cầu xóa',
    example: 5,
  })
  totalRequested: number;

  /**
   * Số đơn hàng xóa thành công
   * @example 3
   */
  @ApiProperty({
    description: 'Số đơn hàng xóa thành công',
    example: 3,
  })
  successCount: number;

  /**
   * Số đơn hàng xóa thất bại
   * @example 2
   */
  @ApiProperty({
    description: 'Số đơn hàng xóa thất bại',
    example: 2,
  })
  failureCount: number;

  /**
   * Kết quả chi tiết cho từng đơn hàng
   */
  @ApiProperty({
    description: 'Kết quả chi tiết cho từng đơn hàng',
    type: [BulkDeleteUserOrderResultItemDto],
  })
  results: BulkDeleteUserOrderResultItemDto[];

  /**
   * Thông báo tổng quan
   * @example "Xóa thành công 3/5 đơn hàng"
   */
  @ApiProperty({
    description: 'Thông báo tổng quan',
    example: 'Xóa thành công 3/5 đơn hàng',
  })
  message: string;
}
