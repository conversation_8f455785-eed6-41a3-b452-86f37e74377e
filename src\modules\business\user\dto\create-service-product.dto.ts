import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  Min,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  ProductImageDto, 
  ProductImagesDto, 
  ProductTagDto, 
  ProductTagsDto, 
  CustomFieldDto, 
  ProductMetadataDto 
} from './create-physical-product.dto';

/**
 * DTO cho giá sản phẩm dịch vụ
 */
export class ServiceProductPriceDto {
  @ApiProperty({
    description: 'Giá gốc',
    example: 2000000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  originalPrice?: number;

  @ApiProperty({
    description: 'Giá bán',
    example: 1500000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  salePrice?: number;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;
}

/**
 * DTO cho gói dịch vụ
 */
export class ServicePackageDto {
  @ApiProperty({
    description: 'ID của service package',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Gói tư vấn cơ bản',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Mô tả',
    example: 'Gói tư vấn cơ bản bao gồm phân tích hiện trạng và đưa ra giải pháp',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Giá gói',
    example: 5000000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({
    description: 'Thời gian thực hiện (hours)',
    example: 40,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  durationHours?: number;

  @ApiProperty({
    description: 'Danh sách tính năng',
    type: [String],
    example: ['Phân tích hiện trạng', 'Đưa ra giải pháp', 'Hỗ trợ triển khai'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];

  @ApiProperty({
    description: 'Hình ảnh gói dịch vụ',
    type: [ProductImageDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageDto)
  images?: ProductImageDto[];

  @ApiProperty({
    description: 'Có phải gói phổ biến không',
    example: false,
    required: false,
  })
  @IsOptional()
  isPopular?: boolean;
}

/**
 * DTO cho việc tạo sản phẩm dịch vụ
 */
export class CreateServiceProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Dịch vụ tư vấn Marketing',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là SERVICE',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.SERVICE,
  })
  @IsEnum(ProductTypeEnum)
  productType: ProductTypeEnum.SERVICE;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: ServiceProductPriceDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => ServiceProductPriceDto)
  price: ServiceProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Dịch vụ tư vấn Marketing chuyên nghiệp với đội ngũ chuyên gia giàu kinh nghiệm',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  images?: ProductImagesDto;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Danh sách gói dịch vụ',
    type: [ServicePackageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ServicePackageDto)
  servicePackages: ServicePackageDto[];

  @ApiProperty({
    description: 'Danh sách hình ảnh cho advanced info',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  advancedImages?: ProductImagesDto;
}
