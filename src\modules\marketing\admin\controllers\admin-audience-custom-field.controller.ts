import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { AdminAudienceCustomFieldService } from '../services/admin-audience-custom-field.service';
import { 
  CreateCustomFieldDto, 
  UpdateCustomFieldDto, 
  CustomFieldResponseDto,
  BulkUpdateCustomFieldsDto
} from '../dto/audience';
import { ApiResponseDto } from '@common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * <PERSON> xử lý các API liên quan đến giá trị trường tùy chỉnh của audience (Admin)
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_MARKETING_CUSTOM_FIELD)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/marketing/audiences/:audienceId/custom-fields')
export class AdminAudienceCustomFieldController {
  constructor(private readonly customFieldService: AdminAudienceCustomFieldService) {}

  /**
   * Tạo mới giá trị trường tùy chỉnh cho audience
   * @param audienceId ID của audience
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin giá trị trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới giá trị trường tùy chỉnh cho audience' })
  @ApiResponse({
    status: 201,
    description: 'Giá trị trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
  )
  async create(
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Body() createDto: CreateCustomFieldDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldService.create(audienceId, createDto);
    return wrapResponse(result, 'Tạo giá trị trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật hàng loạt giá trị trường tùy chỉnh cho audience
   * Sẽ xóa tất cả các giá trị hiện có và thay thế bằng các giá trị mới
   * @param audienceId ID của audience
   * @param updateDto Dữ liệu cập nhật
   * @returns Danh sách các trường tùy chỉnh đã cập nhật
   */
  @Put()
  @ApiOperation({ summary: 'Cập nhật hàng loạt giá trị trường tùy chỉnh cho audience' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách giá trị trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema([CustomFieldResponseDto]),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async bulkUpdate(
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Body() updateDto: BulkUpdateCustomFieldsDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto[]>> {
    const result = await this.customFieldService.bulkUpdate(audienceId, updateDto);
    return wrapResponse(result, 'Cập nhật hàng loạt giá trị trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật giá trị trường tùy chỉnh
   * @param audienceId ID của audience
   * @param id ID của giá trị trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin giá trị trường tùy chỉnh đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật giá trị trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Giá trị trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async update(
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomFieldDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldService.update(id, updateDto.fieldValue);
    return wrapResponse(result, 'Cập nhật giá trị trường tùy chỉnh thành công');
  }

  /**
   * Xóa giá trị trường tùy chỉnh
   * @param audienceId ID của audience
   * @param id ID của giá trị trường tùy chỉnh
   * @returns Thông tin giá trị trường tùy chỉnh đã xóa
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa giá trị trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Giá trị trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
  )
  async delete(
    @Param('audienceId', ParseIntPipe) audienceId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldService.delete(id);
    return wrapResponse(result, 'Xóa giá trị trường tùy chỉnh thành công');
  }

  /**
   * Lấy danh sách giá trị trường tùy chỉnh của audience
   * @param audienceId ID của audience
   * @returns Danh sách giá trị trường tùy chỉnh
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách giá trị trường tùy chỉnh của audience' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách giá trị trường tùy chỉnh thành công',
    schema: ApiResponseDto.getSchema([CustomFieldResponseDto]),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_QUERY_FAILED,
  )
  async findByAudienceId(
    @Param('audienceId', ParseIntPipe) audienceId: number,
  ): Promise<ApiResponseDto<CustomFieldResponseDto[]>> {
    const result = await this.customFieldService.findByAudienceId(audienceId);
    return wrapResponse(result, 'Lấy danh sách giá trị trường tùy chỉnh thành công');
  }
}
