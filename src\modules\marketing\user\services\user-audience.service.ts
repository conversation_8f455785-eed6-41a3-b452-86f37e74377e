import { Injectable, NotFoundException } from '@nestjs/common';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { UserTagRepository } from '../repositories/user-tag.repository';
import { In, Like, FindOptionsWhere, Or } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { CreateAudienceDto, UpdateAudienceDto, AudienceResponseDto, CustomFieldResponseDto, AudienceQueryDto } from '../dto/audience';
import { CreateAvatarUploadUrlDto, AvatarUploadUrlResponseDto, UpdateAvatarDto } from '../dto/audience/avatar-upload.dto';
import { TagResponseDto } from '../dto/tag';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { UserAudience, UserAudienceCustomField, UserTag } from '../entities';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { S3Service } from '@/shared/services/s3.service';
import { generateS3Key, CategoryFolderEnum } from '@/shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum, FileSizeEnum } from '@/shared/utils';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { AppException } from '@common/exceptions';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';

/**
 * Service xử lý logic liên quan đến audience
 */
@Injectable()
export class UserAudienceService {
  constructor(
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userAudienceCustomFieldRepository: UserAudienceCustomFieldRepository,
    private readonly userTagRepository: UserTagRepository,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo audience mới
   * @param userId ID của người dùng
   * @param createAudienceDto Dữ liệu tạo audience
   * @returns Thông tin audience đã tạo
   */
  @Transactional()
  async create(userId: number, createAudienceDto: CreateAudienceDto): Promise<AudienceResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    // Validate email không được trùng lặp
    if (createAudienceDto.email && createAudienceDto.email.trim()) {
      const existingEmailAudience = await this.userAudienceRepository.findByEmail(createAudienceDto.email, userId);
      if (existingEmailAudience) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
          `Email ${createAudienceDto.email} đã được sử dụng bởi khách hàng khác`
        );
      }
    }

    // Validate phone không được trùng lặp
    if (createAudienceDto.phone && createAudienceDto.phone.trim()) {
      const existingPhoneAudience = await this.userAudienceRepository.findByPhone(createAudienceDto.phone, userId);
      if (existingPhoneAudience) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
          `Số điện thoại ${createAudienceDto.phone} đã được sử dụng bởi khách hàng khác`
        );
      }
    }

    // Tạo audience
    const audience = new UserAudience();
    audience.userId = userId;
    audience.name = createAudienceDto.name;
    audience.email = createAudienceDto.email || '';
    audience.phone = createAudienceDto.phone || '';
    audience.countryCode = createAudienceDto.countryCode || '+84';
    audience.avatar = createAudienceDto.avatar || null;
    audience.createdAt = now;
    audience.updatedAt = now;

    const savedAudience = await this.userAudienceRepository.save(audience);

    // Tạo các trường tùy chỉnh
    const customFields: UserAudienceCustomField[] = [];
    if (createAudienceDto.customFields && createAudienceDto.customFields.length > 0) {
      for (const fieldDto of createAudienceDto.customFields) {
        const customField = new UserAudienceCustomField();
        customField.audienceId = (savedAudience as UserAudience).id;
        customField.fieldId = fieldDto.fieldId;
        customField.fieldValue = fieldDto.fieldValue;
        customField.createdAt = now;
        customField.updatedAt = now;
        customFields.push(customField);
      }

      await this.userAudienceCustomFieldRepository.save(customFields);
    }

    // Lấy thông tin tags nếu có
    let tags: UserTag[] = [];
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      tags = await this.userTagRepository.find({
        where: {
          id: In(createAudienceDto.tagIds),
          userId,
        },
      });
    }

    // Đảm bảo savedAudience là một đối tượng UserAudience, không phải mảng
    return this.mapToDto(savedAudience as UserAudience, customFields, tags);
  }

  /**
   * Cập nhật audience
   * @param userId ID của người dùng
   * @param id ID của audience
   * @param updateAudienceDto Dữ liệu cập nhật audience
   * @returns Thông tin audience đã cập nhật
   */
  @Transactional()
  async update(userId: number, id: number, updateAudienceDto: UpdateAudienceDto): Promise<AudienceResponseDto> {
    const audience = await this.userAudienceRepository.findOne({ where: { id, userId } });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    // Validate email không được trùng lặp (trừ chính audience hiện tại)
    if (updateAudienceDto.email !== undefined && updateAudienceDto.email && updateAudienceDto.email.trim()) {
      const isEmailExists = await this.userAudienceRepository.isEmailExists(updateAudienceDto.email, userId, id);
      if (isEmailExists) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
          `Email ${updateAudienceDto.email} đã được sử dụng bởi khách hàng khác`
        );
      }
    }

    // Validate phone không được trùng lặp (trừ chính audience hiện tại)
    if (updateAudienceDto.phone !== undefined && updateAudienceDto.phone && updateAudienceDto.phone.trim()) {
      const isPhoneExists = await this.userAudienceRepository.isPhoneExists(updateAudienceDto.phone, userId, id);
      if (isPhoneExists) {
        throw new AppException(
          MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
          `Số điện thoại ${updateAudienceDto.phone} đã được sử dụng bởi khách hàng khác`
        );
      }
    }

    const now = Math.floor(Date.now() / 1000);

    // Cập nhật thông tin audience
    if (updateAudienceDto.name !== undefined) {
      audience.name = updateAudienceDto.name;
    }

    if (updateAudienceDto.email !== undefined) {
      audience.email = updateAudienceDto.email;
    }

    if (updateAudienceDto.phone !== undefined) {
      audience.phone = updateAudienceDto.phone;
    }

    if (updateAudienceDto.countryCode !== undefined) {
      audience.countryCode = updateAudienceDto.countryCode;
    }

    if (updateAudienceDto.avatar !== undefined) {
      audience.avatar = updateAudienceDto.avatar;
    }

    audience.updatedAt = now;
    const updatedAudience = await this.userAudienceRepository.save(audience);

    // Cập nhật các trường tùy chỉnh
    let customFields: UserAudienceCustomField[] = [];
    if (updateAudienceDto.customFields !== undefined) {
      // Xóa các trường tùy chỉnh hiện tại
      await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

      // Tạo các trường tùy chỉnh mới
      if (updateAudienceDto.customFields.length > 0) {
        for (const fieldDto of updateAudienceDto.customFields) {
          const customField = new UserAudienceCustomField();
          customField.audienceId = id;
          customField.fieldId = fieldDto.fieldId;
          customField.fieldValue = fieldDto.fieldValue;
          customField.createdAt = now;
          customField.updatedAt = now;
          customFields.push(customField);
        }

        const savedCustomFields = await this.userAudienceCustomFieldRepository.save(customFields);
        customFields = Array.isArray(savedCustomFields) ? savedCustomFields : [savedCustomFields];
      }
    } else {
      // Lấy các trường tùy chỉnh hiện tại
      customFields = await this.userAudienceCustomFieldRepository.find({ where: { audienceId: id } });
    }

    // Lấy thông tin tags
    let tags: UserTag[] = [];
    if (updateAudienceDto.tagIds !== undefined) {
      if (updateAudienceDto.tagIds.length > 0) {
        tags = await this.userTagRepository.find({
          where: {
            id: In(updateAudienceDto.tagIds),
            userId,
          },
        });
      }
    } else {
      // TODO: Nếu có bảng liên kết audience-tag, cần lấy tags hiện tại
    }

    // Đảm bảo updatedAudience là một đối tượng UserAudience, không phải mảng
    return this.mapToDto(updatedAudience as UserAudience, customFields, tags);
  }

  /**
   * Xóa audience
   * @param userId ID của người dùng
   * @param id ID của audience
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(userId: number, id: number): Promise<boolean> {
    const audience = await this.userAudienceRepository.findOne({ where: { id, userId } });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    // Xóa các trường tùy chỉnh
    await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

    // Xóa audience
    await this.userAudienceRepository.remove(audience);
    return true;
  }

  /**
   * Xóa nhiều audience
   * @param userId ID của người dùng
   * @param ids Danh sách ID audience cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(userId: number, ids: number[]): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const audience = await this.userAudienceRepository.findOne({ where: { id, userId } });
        if (!audience) {
          failedIds.push(id);
          continue;
        }

        // Xóa các trường tùy chỉnh
        await this.userAudienceCustomFieldRepository.delete({ audienceId: id });

        // Xóa audience
        await this.userAudienceRepository.remove(audience);
        deletedIds.push(id);
      } catch (error) {
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = failedCount > 0
      ? `Đã xóa ${deletedCount} audience thành công, ${failedCount} audience không thể xóa`
      : `Đã xóa ${deletedCount} audience thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy danh sách audience của người dùng với phân trang và filter
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách audience với phân trang
   */
  async findAll(userId: number, query: AudienceQueryDto): Promise<PaginatedResponseDto<AudienceResponseDto>> {
    const { page = 1, limit = 10, search, name, email, phone, tagId, customFieldName, customFieldValue, sortBy = 'createdAt', sortDirection = 'DESC' } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    let where: FindOptionsWhere<UserAudience> | FindOptionsWhere<UserAudience>[] = { userId };

    // Thêm điều kiện tìm kiếm tổng hợp (search trong name, email, phone)
    if (search) {
      where = [
        { userId, name: Like(`%${search}%`) },
        { userId, email: Like(`%${search}%`) },
        { userId, phone: Like(`%${search}%`) }
      ];
    } else {
      // Thêm điều kiện tìm kiếm theo tên
      if (name) {
        (where as FindOptionsWhere<UserAudience>).name = Like(`%${name}%`);
      }

      // Thêm điều kiện tìm kiếm theo email
      if (email) {
        (where as FindOptionsWhere<UserAudience>).email = Like(`%${email}%`);
      }

      // Thêm điều kiện tìm kiếm theo số điện thoại
      if (phone) {
        (where as FindOptionsWhere<UserAudience>).phone = Like(`%${phone}%`);
      }
    }

    // Đếm tổng số audience
    const total = await this.userAudienceRepository.count({ where });

    // Lấy danh sách audience với phân trang và sắp xếp
    const audiences = await this.userAudienceRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Lấy danh sách ID của audience
    const audienceIds = audiences.map(a => a.id);

    // Lấy tất cả các trường tùy chỉnh của các audience
    let customFieldsQuery: FindOptionsWhere<UserAudienceCustomField> = { audienceId: In(audienceIds) };

    // Thêm điều kiện tìm kiếm theo giá trị trường tùy chỉnh
    if (customFieldValue) {
      customFieldsQuery.fieldValue = Like(`%${customFieldValue}%`);
    }

    // TODO: Để tìm kiếm theo tên trường, cần join với bảng custom field definitions
    // Hiện tại chỉ hỗ trợ tìm kiếm theo giá trị trường
    if (customFieldName) {
      // Tạm thời bỏ qua tìm kiếm theo tên trường vì cần join với bảng definitions
      // Có thể implement sau bằng cách sử dụng query builder
    }

    const customFields = await this.userAudienceCustomFieldRepository.find({
      where: customFieldsQuery,
    });

    // Lấy tags nếu có tagId
    let tags: UserTag[] = [];
    if (tagId) {
      tags = await this.userTagRepository.find({
        where: { id: tagId, userId },
      });
    }

    // Chuyển đổi kết quả thành DTO
    const data: AudienceResponseDto[] = [];
    for (const audience of audiences) {
      const audienceCustomFields = customFields.filter(cf => cf.audienceId === audience.id);
      const audienceTags = tagId ? tags : [];
      data.push(this.mapToDto(audience, audienceCustomFields, audienceTags));
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy thông tin audience theo ID
   * @param userId ID của người dùng
   * @param id ID của audience
   * @returns Thông tin audience
   */
  async findOne(userId: number, id: number): Promise<AudienceResponseDto> {
    const audience = await this.userAudienceRepository.findOne({ where: { id, userId } });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${id} không tồn tại`);
    }

    const customFields = await this.userAudienceCustomFieldRepository.find({ where: { audienceId: id } });
    // TODO: Nếu có bảng liên kết audience-tag, cần lấy tags

    return this.mapToDto(audience, customFields, []);
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param audience Entity audience
   * @param customFields Danh sách các trường tùy chỉnh
   * @param tags Danh sách các tag
   * @returns DTO audience
   */
  private mapToDto(
    audience: UserAudience,
    customFields: UserAudienceCustomField[],
    tags: UserTag[],
  ): AudienceResponseDto {
    const dto = new AudienceResponseDto();
    dto.id = audience.id;
    dto.name = audience.name;
    dto.email = audience.email;
    dto.phone = audience.phone;
    dto.countryCode = audience.countryCode;
    dto.avatar = audience.avatar;
    dto.createdAt = audience.createdAt;
    dto.updatedAt = audience.updatedAt;

    // Chuyển đổi các trường tùy chỉnh
    dto.customFields = customFields.map(field => {
      const fieldDto = new CustomFieldResponseDto();
      fieldDto.id = field.id;
      fieldDto.audienceId = field.audienceId;
      fieldDto.fieldId = field.fieldId;
      fieldDto.fieldValue = field.fieldValue;
      fieldDto.createdAt = field.createdAt;
      fieldDto.updatedAt = field.updatedAt;
      return fieldDto;
    });

    // Chuyển đổi các tag
    dto.tags = tags.map(tag => {
      const tagDto = new TagResponseDto();
      tagDto.id = tag.id;
      tagDto.name = tag.name;
      tagDto.color = tag.color;
      tagDto.createdAt = tag.createdAt;
      tagDto.updatedAt = tag.updatedAt;
      return tagDto;
    });

    return dto;
  }

  /**
   * Tạo presigned URL để upload avatar
   * @param userId ID của người dùng
   * @param audienceId ID của audience
   * @param createAvatarUploadUrlDto Dữ liệu tạo URL upload
   * @returns Thông tin URL upload
   */
  async createAvatarUploadUrl(
    userId: number,
    audienceId: number,
    createAvatarUploadUrlDto: CreateAvatarUploadUrlDto,
  ): Promise<AvatarUploadUrlResponseDto> {
    // Kiểm tra audience tồn tại và thuộc về user
    const audience = await this.userAudienceRepository.findOne({
      where: { id: audienceId, userId }
    });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${audienceId} không tồn tại`);
    }

    // Tạo S3 key cho avatar
    const s3Key = generateS3Key({
      baseFolder: 'marketing',
      categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
      useTimeFolder: true,
      prefix: `user_${userId}`,
      fileName: `avatar.${createAvatarUploadUrlDto.mediaType.split('_')[1].toLowerCase()}`,
    });

    // Tạo presigned URL
    const uploadUrl = await this.s3Service.createPresignedWithID(
      s3Key,
      TimeIntervalEnum.ONE_HOUR, // URL hết hạn sau 1 giờ
      createAvatarUploadUrlDto.mediaType as any, // Cast to MediaType
      FileSizeEnum.FIVE_MB, // Giới hạn 5MB cho avatar
    );

    // Tính thời gian hết hạn
    const expiresAt = Math.floor(Date.now() / 1000) + (60 * 60); // 1 giờ

    return {
      uploadUrl,
      s3Key,
      expiresAt,
    };
  }

  /**
   * Cập nhật avatar sau khi upload thành công
   * @param userId ID của người dùng
   * @param audienceId ID của audience
   * @param updateAvatarDto Dữ liệu cập nhật avatar
   * @returns Thông tin audience đã cập nhật
   */
  async updateAvatar(
    userId: number,
    audienceId: number,
    updateAvatarDto: UpdateAvatarDto,
  ): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại và thuộc về user
    const audience = await this.userAudienceRepository.findOne({
      where: { id: audienceId, userId }
    });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${audienceId} không tồn tại`);
    }

    const now = Math.floor(Date.now() / 1000);

    // Xóa avatar cũ nếu có
    if (audience.avatar) {
      try {
        await this.s3Service.deleteFile(audience.avatar);
      } catch (error) {
        // Log lỗi nhưng không dừng quá trình cập nhật
        console.warn(`Không thể xóa avatar cũ: ${audience.avatar}`, error);
      }
    }

    // Cập nhật avatar mới
    audience.avatar = updateAvatarDto.s3Key;
    audience.updatedAt = now;

    const updatedAudience = await this.userAudienceRepository.save(audience);

    // Lấy thông tin đầy đủ để trả về
    const customFields = await this.userAudienceCustomFieldRepository.find({
      where: { audienceId }
    });

    return this.mapToDto(updatedAudience as UserAudience, customFields, []);
  }

  /**
   * Xóa avatar của audience
   * @param userId ID của người dùng
   * @param audienceId ID của audience
   * @returns Thông tin audience đã cập nhật
   */
  async removeAvatar(userId: number, audienceId: number): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại và thuộc về user
    const audience = await this.userAudienceRepository.findOne({
      where: { id: audienceId, userId }
    });
    if (!audience) {
      throw new NotFoundException(`Audience với ID ${audienceId} không tồn tại`);
    }

    const now = Math.floor(Date.now() / 1000);

    // Xóa avatar trên S3 nếu có
    if (audience.avatar) {
      try {
        await this.s3Service.deleteFile(audience.avatar);
      } catch (error) {
        // Log lỗi nhưng không dừng quá trình cập nhật
        console.warn(`Không thể xóa avatar: ${audience.avatar}`, error);
      }
    }

    // Cập nhật database
    audience.avatar = null;
    audience.updatedAt = now;

    const updatedAudience = await this.userAudienceRepository.save(audience);

    // Lấy thông tin đầy đủ để trả về
    const customFields = await this.userAudienceCustomFieldRepository.find({
      where: { audienceId }
    });

    return this.mapToDto(updatedAudience as UserAudience, customFields, []);
  }
}
