import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloAdsPerformance } from '../entities/zalo-ads-performance.entity';

/**
 * Repository cho ZaloAdsPerformance
 */
@Injectable()
export class ZaloAdsPerformanceRepository {
  constructor(
    @InjectRepository(ZaloAdsPerformance)
    private readonly repository: Repository<ZaloAdsPerformance>,
  ) {}

  /**
   * Tìm kiếm nhiều Ads Performance
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách Ads Performance
   */
  async find(options?: FindManyOptions<ZaloAdsPerformance>): Promise<ZaloAdsPerformance[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một Ads Performance
   * @param options Tùy chọn tìm kiếm
   * @returns Ads Performance hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloAdsPerformance>): Promise<ZaloAdsPerformance | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm Ads Performance theo ID
   * @param id ID của Ads Performance
   * @returns Ads Performance hoặc null
   */
  async findById(id: number): Promise<ZaloAdsPerformance | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tất cả Ads Performance của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách Ads Performance
   */
  async findByUserId(userId: number): Promise<ZaloAdsPerformance[]> {
    return this.repository.find({ where: { userId } });
  }

  /**
   * Tìm tất cả Ads Performance của một tài khoản Ads
   * @param adsAccountId ID của tài khoản Ads
   * @returns Danh sách Ads Performance
   */
  async findByAdsAccountId(adsAccountId: string): Promise<ZaloAdsPerformance[]> {
    return this.repository.find({ where: { adsAccountId } });
  }

  /**
   * Tìm tất cả Ads Performance của một chiến dịch
   * @param campaignId ID của chiến dịch
   * @returns Danh sách Ads Performance
   */
  async findByCampaignId(campaignId: string): Promise<ZaloAdsPerformance[]> {
    return this.repository.find({ where: { campaignId } });
  }

  /**
   * Tạo mới Ads Performance
   * @param data Dữ liệu Ads Performance
   * @returns Ads Performance đã tạo
   */
  async create(data: Partial<ZaloAdsPerformance>): Promise<ZaloAdsPerformance> {
    const adsPerformance = this.repository.create(data);
    return this.repository.save(adsPerformance);
  }

  /**
   * Cập nhật Ads Performance
   * @param id ID của Ads Performance
   * @param data Dữ liệu cập nhật
   * @returns Ads Performance đã cập nhật
   */
  async update(id: number, data: Partial<ZaloAdsPerformance>): Promise<ZaloAdsPerformance | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa Ads Performance
   * @param id ID của Ads Performance
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng Ads Performance
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng Ads Performance
   */
  async count(options?: FindManyOptions<ZaloAdsPerformance>): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Tính tổng các chỉ số performance
   * @param options Tùy chọn tìm kiếm
   * @returns Tổng các chỉ số
   */
  async calculateTotals(options?: FindManyOptions<ZaloAdsPerformance>): Promise<{
    totalSpend: number;
    totalRevenue: number;
    totalImpressions: number;
    totalClicks: number;
    totalConversions: number;
  }> {
    const performances = await this.find(options);
    
    return performances.reduce(
      (totals, performance) => ({
        totalSpend: totals.totalSpend + Number(performance.spend),
        totalRevenue: totals.totalRevenue + Number(performance.revenue),
        totalImpressions: totals.totalImpressions + Number(performance.impressions),
        totalClicks: totals.totalClicks + Number(performance.clicks),
        totalConversions: totals.totalConversions + Number(performance.conversions),
      }),
      {
        totalSpend: 0,
        totalRevenue: 0,
        totalImpressions: 0,
        totalClicks: 0,
        totalConversions: 0,
      }
    );
  }
}
