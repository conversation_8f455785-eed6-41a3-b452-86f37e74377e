import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { AgentInfoDto, FacebookPageResponseDto } from '../dto/facebook/facebook-page-response.dto';
import { FacebookPage } from '../../entities/facebook-page.entity';

/**
 * Interface cho dữ liệu Facebook page từ database query
 */
interface FacebookPageRawData {
  id: string;
  facebook_page_id: string;
  facebook_personal_id: string;
  page_name: string;
  avatar_page?: string;
  is_active: boolean;
  agent_id?: string;
  is_error: boolean;
  agent_name?: string;
  agent_avatar?: string;
  facebook_personal_name: string;
}

/**
 * Mapper để chuyển đổi dữ liệu Facebook page thành DTO
 */
export class FacebookPageMapper {
  /**
   * Chuyển đổi dữ liệu Facebook page từ database thành DTO
   * @param page Dữ liệu Facebook page từ database
   * @param cdnService Service để tạo CDN URL
   * @returns Facebook page DTO
   */
  static toDto(page: FacebookPageRawData, cdnService: CdnService): FacebookPageResponseDto {
    return {
      facebookPageId: page.id,
      facebookPersonalId: page.facebook_personal_id,
      facebookPersonalName: page.facebook_personal_name,
      pageName: page.page_name,
      // Thêm CDN URL cho avatar nếu có
      avatarPage: page.avatar_page ? cdnService.generateUrlView(page.avatar_page, TimeIntervalEnum.ONE_DAY) : null,
      isActive: page.is_active,
      agentId: page.agent_id,
      isError: page.is_error,
      agent: page.agent_id && page.agent_name ? {
        id: page.agent_id,
        name: page.agent_name,
        avatar: page.agent_avatar ? cdnService.generateUrlView(page.agent_avatar, TimeIntervalEnum.ONE_DAY) : null
      } : undefined
    };
  }

  /**
   * Chuyển đổi danh sách dữ liệu Facebook page từ database thành danh sách DTO
   * @param pages Danh sách dữ liệu Facebook page từ database
   * @param cdnService Service để tạo CDN URL
   * @returns Danh sách Facebook page DTO
   */
  static toDtoList(pages: FacebookPageRawData[], cdnService: CdnService): FacebookPageResponseDto[] {
    return pages.map(page => this.toDto(page, cdnService));
  }
}
