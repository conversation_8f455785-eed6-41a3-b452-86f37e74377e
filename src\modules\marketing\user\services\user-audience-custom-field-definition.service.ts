import { Injectable, Logger } from '@nestjs/common';
import { UserAudienceCustomFieldDefinitionRepository } from '../repositories/user-audience-custom-field-definition.repository';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';

import { UserAudienceCustomFieldDefinition } from '../entities/user-audience-custom-field-definition.entity';
import { AudienceCustomFieldDefinitionQueryDto, AudienceCustomFieldDefinitionResponseDto, CreateAudienceCustomFieldDefinitionDto, UpdateAudienceCustomFieldDefinitionDto } from '../dto';
import { SortDirection } from '@common/dto/query.dto';
import { PaginatedResult } from '@/common/response';
import { BulkDeleteResponseDto } from '@modules/marketing/common/dto/bulk-delete.dto';

/**
 * Service xử lý logic nghiệp vụ cho trường tùy chỉnh của người dùng
 */
@Injectable()
export class UserAudienceCustomFieldDefinitionService {
  private readonly logger = new Logger(UserAudienceCustomFieldDefinitionService.name);

  constructor(
    private readonly customFieldRepository: UserAudienceCustomFieldDefinitionRepository,
  ) {}

  /**
   * Tạo mới trường tùy chỉnh
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Transactional()
  async create(
    userId: number,
    createDto: CreateAudienceCustomFieldDefinitionDto,
  ): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Kiểm tra trường tùy chỉnh đã tồn tại chưa
      const existingField = await this.customFieldRepository.findOne({
        where: { userId, fieldKey: createDto.fieldKey },
      });

      if (existingField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
          `Trường tùy chỉnh với định danh '${createDto.fieldKey}' đã tồn tại`,
        );
      }

      // Tạo mới trường tùy chỉnh
      const customField = await this.customFieldRepository.create({
        fieldKey: createDto.fieldKey,
        userId,
        displayName: createDto.displayName,
        dataType: createDto.dataType,
        description: createDto.description,
        tags: createDto.tags || [],
        config: createDto.config || {},
      });

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(customField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        `Tạo trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param userId ID của người dùng
   * @param id ID của trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async update(
    userId: number,
    id: number,
    updateDto: UpdateAudienceCustomFieldDefinitionDto,
  ): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Tìm kiếm trường tùy chỉnh
      const customField = await this.customFieldRepository.findOne({
        where: { id, userId },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id}`,
        );
      }

      // Cập nhật trường tùy chỉnh
      if (updateDto.displayName) {
        customField.displayName = updateDto.displayName;
      }
      if (updateDto.dataType) {
        customField.dataType = updateDto.dataType;
      }
      if (updateDto.description !== undefined) {
        customField.description = updateDto.description;
      }
      if (updateDto.tags !== undefined) {
        customField.tags = updateDto.tags;
      }
      if (updateDto.config !== undefined) {
        customField.config = updateDto.config;
      }

      // Lưu trường tùy chỉnh
      const updatedField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(updatedField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
        `Cập nhật trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa trường tùy chỉnh
   * @param userId ID của người dùng
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Transactional()
  async delete(userId: number, id: number): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Tìm kiếm trường tùy chỉnh
      const customField = await this.customFieldRepository.findOne({
        where: { id, userId },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id}`,
        );
      }

      // Lưu thông tin trường tùy chỉnh trước khi xóa
      const deletedField = { ...customField };

      // Xóa trường tùy chỉnh
      await this.customFieldRepository.remove(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(deletedField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
        `Xóa trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều trường tùy chỉnh
   * @param userId ID của người dùng
   * @param ids Danh sách ID trường tùy chỉnh cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(userId: number, ids: number[]): Promise<BulkDeleteResponseDto> {
    console.log('Service received ids:', JSON.stringify(ids, null, 2));
    console.log('Type of ids:', typeof ids);
    console.log('Is array:', Array.isArray(ids));
    console.log('ids length:', ids?.length);

    // Validate input
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
        'Danh sách ID không hợp lệ hoặc rỗng',
      );
    }

    // Validate each ID
    for (const id of ids) {
      if (!Number.isInteger(id) || id <= 0 || isNaN(id)) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
          `ID không hợp lệ: ${id}`,
        );
      }
    }

    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const customField = await this.customFieldRepository.findOne({
          where: { id, userId }
        });

        if (!customField) {
          failedIds.push(id);
          continue;
        }

        // Xóa trường tùy chỉnh
        await this.customFieldRepository.remove(customField);
        deletedIds.push(id);
      } catch (error) {
        this.logger.error(`Error deleting custom field ${id}: ${error.message}`, error.stack);
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;

    let message: string;
    if (failedCount === 0) {
      message = `Đã xóa ${deletedCount} trường tùy chỉnh thành công`;
    } else if (deletedCount === 0) {
      message = `Không thể xóa ${failedCount} trường tùy chỉnh`;
    } else {
      message = `Đã xóa ${deletedCount} trường tùy chỉnh thành công, ${failedCount} trường tùy chỉnh không thể xóa`;
    }

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Lấy thông tin trường tùy chỉnh
   * @param userId ID của người dùng
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh
   */
  async findOne(userId: number, id: number): Promise<AudienceCustomFieldDefinitionResponseDto> {
    try {
      // Tìm kiếm trường tùy chỉnh
      const customField = await this.customFieldRepository.findOne({
        where: { id, userId },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id}`,
        );
      }

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(customField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding custom field: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
        `Không tìm thấy trường tùy chỉnh: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách trường tùy chỉnh
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh
   */
  async findAll(
    userId: number,
    queryDto: AudienceCustomFieldDefinitionQueryDto,
  ): Promise<PaginatedResult<AudienceCustomFieldDefinitionResponseDto>> {
    try {
      const { page = 1, limit = 10, fieldKey, displayName, dataType, sortBy, sortDirection, search } = queryDto;

      // Sanitize search parameter để tránh lỗi SQL injection và ký tự đặc biệt
      const sanitizedSearch = search ? search.trim().replace(/[%_\\]/g, '\\$&') : undefined;

      // Tính toán offset
      const skip = (page - 1) * limit;

      // Xây dựng điều kiện sắp xếp
      const order: Record<string, 'ASC' | 'DESC'> = {};
      if (sortBy) {
        order[sortBy] = sortDirection || SortDirection.ASC;
      } else {
        order.displayName = SortDirection.ASC;
      }

      let queryBuilder = this.customFieldRepository
        .createQueryBuilder('customField')
        .where('customField.userId = :userId', { userId });

      // Xử lý tìm kiếm chung (search)
      if (sanitizedSearch) {
        queryBuilder = queryBuilder.andWhere(
          '(customField.displayName ILIKE :search OR customField.fieldKey ILIKE :search OR EXISTS (SELECT 1 FROM jsonb_array_elements_text(customField.tags) AS tag WHERE tag ILIKE :search))',
          { search: `%${sanitizedSearch}%` }
        );
      }

      // Xử lý tìm kiếm cụ thể theo từng trường
      if (fieldKey) {
        queryBuilder = queryBuilder.andWhere('customField.fieldKey ILIKE :fieldKey', { fieldKey: `%${fieldKey}%` });
      }

      if (displayName) {
        queryBuilder = queryBuilder.andWhere('customField.displayName ILIKE :displayName', { displayName: `%${displayName}%` });
      }

      if (dataType) {
        queryBuilder = queryBuilder.andWhere('customField.dataType = :dataType', { dataType });
      }

      // Áp dụng sắp xếp
      Object.entries(order).forEach(([field, direction]) => {
        queryBuilder = queryBuilder.addOrderBy(`customField.${field}`, direction);
      });

      // Lấy tổng số items trước
      const totalItems = await queryBuilder.getCount();

      // Sau đó lấy danh sách với phân trang
      const customFields = await queryBuilder.skip(skip).take(limit).getMany();

      // Chuyển đổi sang DTO
      const items = customFields.map((customField) => this.mapToResponseDto(customField));

      // Tạo metadata phân trang theo cấu trúc PaginatedResult
      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding custom fields for userId ${userId}:`, {
        error: error.message,
        stack: error.stack,
        queryParams: queryDto,
        originalSearch: queryDto.search,
      });

      // Ném lại lỗi với thông tin chi tiết hơn
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_QUERY_FAILED,
        `Lỗi khi tìm kiếm trường tùy chỉnh: ${error.message}`,
        {
          userId,
          queryParams: queryDto,
          originalError: error.message,
        }
      );
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   * @param customField Entity trường tùy chỉnh
   * @returns DTO trường tùy chỉnh
   */
  private mapToResponseDto(
    customField: UserAudienceCustomFieldDefinition,
  ): AudienceCustomFieldDefinitionResponseDto {
    return {
      id: customField.id,
      fieldKey: customField.fieldKey,
      userId: customField.userId,
      displayName: customField.displayName,
      dataType: customField.dataType,
      description: customField.description,
      tags: customField.tags || [],
      config: customField.config || {},
    };
  }
}
