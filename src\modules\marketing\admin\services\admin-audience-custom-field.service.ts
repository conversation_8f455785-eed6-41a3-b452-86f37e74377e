import { Injectable, Logger } from '@nestjs/common';
import { AdminAudienceCustomFieldRepository } from '../repositories/admin-audience-custom-field.repository';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { AdminAudienceCustomField } from '../entities/admin-audience-custom-field.entity';
import { CreateCustomFieldDto, CustomFieldResponseDto } from '../dto/audience';
import { BulkUpdateCustomFieldsDto } from '../dto/audience/bulk-update-custom-fields.dto';

/**
 * Service xử lý logic nghiệp vụ cho giá trị trường tùy chỉnh của audience (Admin)
 */
@Injectable()
export class AdminAudienceCustomFieldService {
  private readonly logger = new Logger(AdminAudienceCustomFieldService.name);

  constructor(
    private readonly customFieldRepository: AdminAudienceCustomFieldRepository,
  ) {}

  /**
   * Tạo mới giá trị trường tùy chỉnh cho audience
   * @param audienceId ID của audience
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Transactional()
  async create(
    audienceId: number,
    createDto: CreateCustomFieldDto,
  ): Promise<CustomFieldResponseDto> {
    try {
      // Kiểm tra trường đã tồn tại cho audience này chưa
      const existingField = await this.customFieldRepository.findOne({
        where: { audienceId, fieldName: createDto.fieldName },
      });

      if (existingField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
          `Trường tùy chỉnh đã tồn tại cho audience này`,
        );
      }

      // Tạo mới giá trị trường tùy chỉnh
      const now = Math.floor(Date.now() / 1000);
      const customField = new AdminAudienceCustomField();
      customField.audienceId = audienceId;
      customField.fieldName = createDto.fieldName;
      customField.fieldValue = createDto.fieldValue;
      customField.fieldType = createDto.fieldType;
      customField.createdAt = now;
      customField.updatedAt = now;

      const savedCustomField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(savedCustomField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating custom field value: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        `Tạo giá trị trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật giá trị trường tùy chỉnh
   * @param id ID của custom field value
   * @param fieldValue Giá trị mới
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async update(
    id: number,
    fieldValue: any,
  ): Promise<CustomFieldResponseDto> {
    try {
      // Tìm kiếm custom field value
      const customField = await this.customFieldRepository.findOne({
        where: { id },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy giá trị trường tùy chỉnh với ID ${id}`,
        );
      }

      // Cập nhật giá trị
      customField.fieldValue = fieldValue;
      customField.updatedAt = Math.floor(Date.now() / 1000);

      const updatedCustomField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(updatedCustomField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating custom field value: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
        `Cập nhật giá trị trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa giá trị trường tùy chỉnh
   * @param id ID của custom field value
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Transactional()
  async delete(id: number): Promise<CustomFieldResponseDto> {
    try {
      // Tìm kiếm custom field value
      const customField = await this.customFieldRepository.findOne({
        where: { id },
      });

      if (!customField) {
        throw new AppException(
          MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy giá trị trường tùy chỉnh với ID ${id}`,
        );
      }

      // Xóa custom field value
      await this.customFieldRepository.delete(id);

      // Chuyển đổi sang DTO
      return this.mapToResponseDto(customField);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting custom field value: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
        `Xóa giá trị trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách giá trị trường tùy chỉnh của audience
   * @param audienceId ID của audience
   * @returns Danh sách giá trị trường tùy chỉnh
   */
  async findByAudienceId(audienceId: number): Promise<CustomFieldResponseDto[]> {
    try {
      const customFields = await this.customFieldRepository.find({
        where: { audienceId },
      });

      return customFields.map(field => this.mapToResponseDto(field));
    } catch (error) {
      this.logger.error(`Error finding custom fields by audience ID: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_QUERY_FAILED,
        `Lấy danh sách trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }



  /**
   * Cập nhật hàng loạt các giá trị trường tùy chỉnh cho audience
   * Sẽ xóa tất cả các giá trị hiện có và thay thế bằng các giá trị mới
   * @param audienceId ID của audience
   * @param updateDto Dữ liệu cập nhật
   * @returns Danh sách các trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async bulkUpdate(
    audienceId: number,
    updateDto: BulkUpdateCustomFieldsDto,
  ): Promise<CustomFieldResponseDto[]> {
    try {
      // Xóa tất cả các giá trị hiện có của audience
      await this.customFieldRepository.delete({ audienceId });

      // Tạo mới các giá trị trường tùy chỉnh
      const now = Math.floor(Date.now() / 1000);
      const customFields: AdminAudienceCustomField[] = updateDto.fields.map(field => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = audienceId;
        customField.fieldName = field.fieldName;
        customField.fieldValue = field.fieldValue;
        customField.fieldType = field.fieldType;
        customField.createdAt = now;
        customField.updatedAt = now;
        return customField;
      });

      const savedCustomFields = await this.customFieldRepository.save(customFields);

      // Chuyển đổi sang DTO
      return savedCustomFields.map(field => this.mapToResponseDto(field));
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error bulk updating custom field values: ${error.message}`, error.stack);
      throw new AppException(
        MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
        `Cập nhật hàng loạt giá trị trường tùy chỉnh thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Chuyển đổi entity sang DTO
   */
  private mapToResponseDto(customField: AdminAudienceCustomField): CustomFieldResponseDto {
    return {
      id: customField.id,
      audienceId: customField.audienceId,
      fieldName: customField.fieldName,
      fieldValue: customField.fieldValue,
      fieldType: customField.fieldType as any,
      createdAt: customField.createdAt,
      updatedAt: customField.updatedAt,
    };
  }
}
