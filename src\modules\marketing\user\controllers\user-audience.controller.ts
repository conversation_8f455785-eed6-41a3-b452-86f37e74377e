import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { UserAudienceService } from '../services/user-audience.service';
import { CreateAudienceDto, UpdateAudienceDto, AudienceResponseDto, AudienceQueryDto } from '../dto/audience';
import { CreateAvatarUploadUrlDto, AvatarUploadUrlResponseDto, UpdateAvatarDto } from '../dto/audience/avatar-upload.dto';
import { PaginatedResponseDto } from '../dto/common';
import { JwtUserGuard } from '@/modules/auth/guards';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { BulkDeleteAudienceDto, BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';

/**
 * Controller xử lý API liên quan đến audience
 */
@ApiTags(SWAGGER_API_TAGS.USER_AUDIENCE)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/audiences')
export class UserAudienceController {
  constructor(private readonly userAudienceService: UserAudienceService) {}

  /**
   * Tạo audience mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo audience mới' })
  @ApiResponse({ status: 201, description: 'Audience đã được tạo thành công', type: AudienceResponseDto })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
  )
  async create(@CurrentUser() user: JwtPayload, @Body() createAudienceDto: CreateAudienceDto): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.create(user.id, createAudienceDto);
    return wrapResponse(result, 'Audience đã được tạo thành công');
  }

  
  /**
   * Lấy danh sách audience với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách audience với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách audience với phân trang',
    type: PaginatedResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/AudienceResponseDto' }
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: AudienceQueryDto
  ): Promise<AppApiResponse<PaginatedResponseDto<AudienceResponseDto>>> {
    const result = await this.userAudienceService.findAll(user.id, query);
    return wrapResponse(result, 'Danh sách audience');
  }

  /**
   * Lấy thông tin audience theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin audience theo ID' })
  @ApiResponse({ status: 200, description: 'Thông tin audience', type: AudienceResponseDto })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async findOne(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.findOne(user.id, +id);
    return wrapResponse(result, 'Thông tin audience');
  }

  /**
   * Cập nhật audience
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật audience' })
  @ApiResponse({ status: 200, description: 'Audience đã được cập nhật thành công', type: AudienceResponseDto })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateAudienceDto: UpdateAudienceDto,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.update(user.id, +id, updateAudienceDto);
    return wrapResponse(result, 'Audience đã được cập nhật thành công');
  }

  /**
   * Xóa audience
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa audience' })
  @ApiResponse({ status: 200, description: 'Audience đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<{ success: boolean }>> {
    const result = await this.userAudienceService.remove(user.id, +id);
    return wrapResponse({ success: result }, 'Audience đã được xóa thành công');
  }

  /**
   * Xóa nhiều audience
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều audience' })
  @ApiResponse({
    status: 200,
    description: 'Xóa audience thành công',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({
    status: 207,
    description: 'Một số audience không thể xóa',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy audience' })
  async bulkDelete(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteAudienceDto
  ): Promise<AppApiResponse<BulkDeleteResponseDto>> {
    const result = await this.userAudienceService.bulkDelete(user.id, bulkDeleteDto.ids);
    return wrapResponse(result, result.message);
  }

  /**
   * Tạo presigned URL để upload avatar
   */
  @Post(':id/avatar/upload-url')
  @ApiOperation({ summary: 'Tạo presigned URL để upload avatar' })
  @ApiResponse({
    status: 201,
    description: 'URL upload đã được tạo thành công',
    type: AvatarUploadUrlResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async createAvatarUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() createAvatarUploadUrlDto: CreateAvatarUploadUrlDto
  ): Promise<AppApiResponse<AvatarUploadUrlResponseDto>> {
    const result = await this.userAudienceService.createAvatarUploadUrl(
      user.id,
      +id,
      createAvatarUploadUrlDto
    );
    return wrapResponse(result, 'URL upload avatar đã được tạo thành công');
  }

  /**
   * Cập nhật avatar sau khi upload thành công
   */
  @Put(':id/avatar')
  @ApiOperation({ summary: 'Cập nhật avatar sau khi upload thành công' })
  @ApiResponse({
    status: 200,
    description: 'Avatar đã được cập nhật thành công',
    type: AudienceResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async updateAvatar(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateAvatarDto: UpdateAvatarDto
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.updateAvatar(user.id, +id, updateAvatarDto);
    return wrapResponse(result, 'Avatar đã được cập nhật thành công');
  }

  /**
   * Xóa avatar của audience
   */
  @Delete(':id/avatar')
  @ApiOperation({ summary: 'Xóa avatar của audience' })
  @ApiResponse({
    status: 200,
    description: 'Avatar đã được xóa thành công',
    type: AudienceResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async removeAvatar(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.removeAvatar(user.id, +id);
    return wrapResponse(result, 'Avatar đã được xóa thành công');
  }
}
