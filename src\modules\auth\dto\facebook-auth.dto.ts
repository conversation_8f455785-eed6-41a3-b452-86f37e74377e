import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho yêu cầu đăng nhập bằng Facebook OAuth2
 */
export class FacebookAuthDto {
  @ApiProperty({
    description: 'Authorization code từ Facebook OAuth2',
    example: 'AQD7veB2u5QZcM...',
  })
  @IsNotEmpty({ message: 'Authorization code không được để trống' })
  @IsString({ message: 'Authorization code phải là chuỗi' })
  code: string;

  @ApiProperty({
    description: 'URL chuyển hướng sau khi xác thực (tùy chọn)',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsString({ message: 'Redirect URI phải là chuỗi' })
  redirectUri?: string;

  @ApiProperty({
    description: 'Mã người giới thiệu (tù<PERSON> chọn)',
    example: 12345,
    required: false,
  })
  @IsNumber({}, { message: 'Mã người giới thiệu phải là số' })
  @IsOptional()
  ref?: number;
}