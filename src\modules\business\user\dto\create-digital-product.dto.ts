import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
  Min,
  IsIn,
} from 'class-validator';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { 
  ProductImageDto, 
  ProductImagesDto, 
  ProductTagDto, 
  ProductTagsDto, 
  CustomFieldDto, 
  ProductMetadataDto 
} from './create-physical-product.dto';

/**
 * DTO cho giá sản phẩm số (hỗ trợ STRING_PRICE)
 */
export class DigitalProductPriceDto {
  @ApiProperty({
    description: 'Gi<PERSON> gốc',
    example: 100000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  originalPrice?: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bán',
    example: 80000,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  salePrice?: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> dạng chuỗi (cho STRING_PRICE)',
    example: '<PERSON><PERSON><PERSON> hệ để biết giá',
    required: false,
  })
  @IsOptional()
  @IsString()
  stringPrice?: string;

  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string;
}

/**
 * DTO cho cấu hình tự động trong digital fulfillment
 */
export class AutomaticConfigDto {
  @ApiProperty({
    description: 'Gửi email tự động',
    example: true,
  })
  sendEmail: boolean;

  @ApiProperty({
    description: 'Template email',
    example: 'Cảm ơn bạn đã mua sản phẩm. Link download: {{downloadLink}}',
    required: false,
  })
  @IsOptional()
  @IsString()
  emailTemplate?: string;

  @ApiProperty({
    description: 'Delay time (seconds)',
    example: 300,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  delaySeconds?: number;
}

/**
 * DTO cho cấu hình thủ công trong digital fulfillment
 */
export class ManualConfigDto {
  @ApiProperty({
    description: 'Hướng dẫn xử lý',
    example: 'Kiểm tra thanh toán và gửi link download qua email trong vòng 24h',
  })
  @IsString()
  @IsNotEmpty()
  instructions: string;

  @ApiProperty({
    description: 'Thời gian xử lý tối đa (hours)',
    example: 24,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxProcessingHours?: number;
}

/**
 * DTO cho digital fulfillment flow
 */
export class DigitalFulfillmentFlowDto {
  @ApiProperty({
    description: 'Loại fulfillment',
    enum: ['AUTOMATIC', 'MANUAL'],
    example: 'AUTOMATIC',
  })
  @IsString()
  @IsIn(['AUTOMATIC', 'MANUAL'])
  type: 'AUTOMATIC' | 'MANUAL';

  @ApiProperty({
    description: 'Cấu hình tự động',
    type: AutomaticConfigDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AutomaticConfigDto)
  automaticConfig?: AutomaticConfigDto;

  @ApiProperty({
    description: 'Cấu hình thủ công',
    type: ManualConfigDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ManualConfigDto)
  manualConfig?: ManualConfigDto;
}

/**
 * DTO cho thông tin download link
 */
export class DownloadInfoDto {
  @ApiProperty({
    description: 'URL download',
    example: 'https://cdn.redai.vn/downloads/product-123.zip',
  })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'Thời gian hết hạn (timestamp)',
    example: 1735689600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  expiresAt?: number;

  @ApiProperty({
    description: 'Số lần download tối đa',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxDownloads?: number;
}

/**
 * DTO cho thông tin access code
 */
export class AccessCodeInfoDto {
  @ApiProperty({
    description: 'Mã truy cập',
    example: 'ABC123XYZ',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'Hướng dẫn sử dụng',
    example: 'Nhập mã này vào trang web để truy cập nội dung',
  })
  @IsString()
  @IsNotEmpty()
  instructions: string;
}

/**
 * DTO cho thông tin tài khoản
 */
export class AccountInfoDto {
  @ApiProperty({
    description: 'Username',
    example: 'user123',
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    description: 'Password',
    example: 'password123',
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    description: 'URL đăng nhập',
    example: 'https://app.example.com/login',
    required: false,
  })
  @IsOptional()
  @IsString()
  loginUrl?: string;
}

/**
 * DTO cho nội dung trực tiếp
 */
export class ContentInfoDto {
  @ApiProperty({
    description: 'Tiêu đề',
    example: 'Hướng dẫn sử dụng sản phẩm',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Nội dung',
    example: 'Đây là nội dung chi tiết của sản phẩm...',
  })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiProperty({
    description: 'Định dạng',
    enum: ['TEXT', 'HTML', 'MARKDOWN'],
    example: 'HTML',
  })
  @IsString()
  @IsIn(['TEXT', 'HTML', 'MARKDOWN'])
  format: 'TEXT' | 'HTML' | 'MARKDOWN';
}

/**
 * DTO cho digital output
 */
export class DigitalOutputDto {
  @ApiProperty({
    description: 'Loại output',
    enum: ['DOWNLOAD_LINK', 'ACCESS_CODE', 'ACCOUNT_INFO', 'CONTENT'],
    example: 'DOWNLOAD_LINK',
  })
  @IsString()
  @IsIn(['DOWNLOAD_LINK', 'ACCESS_CODE', 'ACCOUNT_INFO', 'CONTENT'])
  type: 'DOWNLOAD_LINK' | 'ACCESS_CODE' | 'ACCOUNT_INFO' | 'CONTENT';

  @ApiProperty({
    description: 'Thông tin download link',
    type: DownloadInfoDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DownloadInfoDto)
  downloadInfo?: DownloadInfoDto;

  @ApiProperty({
    description: 'Thông tin access code',
    type: AccessCodeInfoDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AccessCodeInfoDto)
  accessCodeInfo?: AccessCodeInfoDto;

  @ApiProperty({
    description: 'Thông tin tài khoản',
    type: AccountInfoDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AccountInfoDto)
  accountInfo?: AccountInfoDto;

  @ApiProperty({
    description: 'Nội dung trực tiếp',
    type: ContentInfoDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ContentInfoDto)
  content?: ContentInfoDto;
}

/**
 * DTO cho việc tạo sản phẩm số
 */
export class CreateDigitalProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Khóa học lập trình Python',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Loại sản phẩm - luôn là DIGITAL',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.DIGITAL,
  })
  @IsEnum(ProductTypeEnum)
  productType: ProductTypeEnum.DIGITAL;

  @ApiProperty({
    description: 'Loại giá (hỗ trợ STRING_PRICE)',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Thông tin giá sản phẩm',
    type: DigitalProductPriceDto,
    example: {
      originalPrice: 2000000,
      salePrice: 1500000,
      currency: 'VND'
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalProductPriceDto)
  price: DigitalProductPriceDto;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Khóa học lập trình Python từ cơ bản đến nâng cao với 50+ bài học',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm',
    type: ProductImagesDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductImagesDto)
  images?: ProductImagesDto;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: ProductTagsDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductTagsDto)
  tags?: ProductTagsDto;

  @ApiProperty({
    description: 'Metadata chứa custom fields',
    type: ProductMetadataDto,
    required: false,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductMetadataDto)
  metadata?: ProductMetadataDto;

  @ApiProperty({
    description: 'Quy trình xử lý đơn hàng số',
    type: DigitalFulfillmentFlowDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalFulfillmentFlowDto)
  digitalFulfillmentFlow: DigitalFulfillmentFlowDto;

  @ApiProperty({
    description: 'Đầu ra sản phẩm số',
    type: DigitalOutputDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalOutputDto)
  digitalOutput: DigitalOutputDto;
}
