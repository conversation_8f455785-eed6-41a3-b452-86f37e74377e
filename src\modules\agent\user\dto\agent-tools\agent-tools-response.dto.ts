import { ApiProperty } from '@nestjs/swagger';
import { ToolStatusEnum } from '@modules/tools/constants';

/**
 * DTO response cho thông tin tool của agent
 */
export class AgentToolsResponseDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'search_tool',
  })
  toolName: string;

  /**
   * Mô tả chi tiết về chức năng của tool
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết về chức năng của tool',
    example: 'Tool tìm kiếm thông tin từ nhiều nguồn dữ liệu',
    nullable: true,
  })
  toolDescription: string | null;

  /**
   * Thời điểm tạo tool (timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm tạo tool (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối (timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật cuối (timestamp)',
    example: 1703123456789,
  })
  updatedAt: number;
}
