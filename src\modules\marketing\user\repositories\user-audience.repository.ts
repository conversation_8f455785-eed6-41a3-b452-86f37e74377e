import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository, Not } from 'typeorm';
import { UserAudience } from '../entities/user-audience.entity';

/**
 * Repository cho UserAudience
 */
@Injectable()
export class UserAudienceRepository {
  constructor(
    @InjectRepository(UserAudience)
    private readonly repository: Repository<UserAudience>,
  ) {}

  /**
   * Tìm kiếm nhiều audience
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách audience
   */
  async find(options?: FindManyOptions<UserAudience>): Promise<UserAudience[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một audience
   * @param options Tùy chọn tìm kiếm
   * @returns Audience hoặc null
   */
  async findOne(options?: FindOneOptions<UserAudience>): Promise<UserAudience | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng audience
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng audience
   */
  async count(options?: FindManyOptions<UserAudience>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Lưu audience
   * @param audience Audience cần lưu
   * @returns Audience đã lưu
   */
  async save(audience: UserAudience): Promise<UserAudience>;
  async save(audience: UserAudience[]): Promise<UserAudience[]>;
  async save(audience: UserAudience | UserAudience[]): Promise<UserAudience | UserAudience[]> {
    return this.repository.save(audience as any);
  }

  /**
   * Xóa audience
   * @param audience Audience cần xóa
   * @returns Audience đã xóa
   */
  async remove(audience: UserAudience): Promise<UserAudience>;
  async remove(audience: UserAudience[]): Promise<UserAudience[]>;
  async remove(audience: UserAudience | UserAudience[]): Promise<UserAudience | UserAudience[]> {
    return this.repository.remove(audience as any);
  }

  /**
   * Tạo mới audience
   * @param data Dữ liệu audience
   * @returns Audience đã tạo
   */
  async create(data: Partial<UserAudience>): Promise<UserAudience> {
    const audience = this.repository.create(data);
    return this.repository.save(audience);
  }

  /**
   * Tìm audience theo email
   * @param email Email cần tìm
   * @param userId ID của người dùng
   * @returns Audience hoặc null
   */
  async findByEmail(email: string, userId: number): Promise<UserAudience | null> {
    if (!email || !email.trim()) {
      return null;
    }
    return this.repository.findOne({
      where: { email: email.trim(), userId },
    });
  }

  /**
   * Tìm audience theo số điện thoại
   * @param phone Số điện thoại cần tìm
   * @param userId ID của người dùng
   * @returns Audience hoặc null
   */
  async findByPhone(phone: string, userId: number): Promise<UserAudience | null> {
    if (!phone || !phone.trim()) {
      return null;
    }
    return this.repository.findOne({
      where: { phone: phone.trim(), userId },
    });
  }

  /**
   * Kiểm tra email đã tồn tại (trừ audience hiện tại)
   * @param email Email cần kiểm tra
   * @param userId ID của người dùng
   * @param excludeId ID audience cần loại trừ (dùng cho update)
   * @returns true nếu email đã tồn tại
   */
  async isEmailExists(email: string, userId: number, excludeId?: number): Promise<boolean> {
    if (!email || !email.trim()) {
      return false;
    }

    const whereCondition: any = { email: email.trim(), userId };
    if (excludeId) {
      whereCondition.id = Not(excludeId);
    }

    const count = await this.repository.count({ where: whereCondition });
    return count > 0;
  }

  /**
   * Kiểm tra số điện thoại đã tồn tại (trừ audience hiện tại)
   * @param phone Số điện thoại cần kiểm tra
   * @param userId ID của người dùng
   * @param excludeId ID audience cần loại trừ (dùng cho update)
   * @returns true nếu số điện thoại đã tồn tại
   */
  async isPhoneExists(phone: string, userId: number, excludeId?: number): Promise<boolean> {
    if (!phone || !phone.trim()) {
      return false;
    }

    const whereCondition: any = { phone: phone.trim(), userId };
    if (excludeId) {
      whereCondition.id = Not(excludeId);
    }

    const count = await this.repository.count({ where: whereCondition });
    return count > 0;
  }
}
