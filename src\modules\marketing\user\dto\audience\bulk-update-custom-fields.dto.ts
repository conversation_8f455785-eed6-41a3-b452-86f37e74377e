import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsArray, ValidateNested } from 'class-validator';
import { CreateCustomFieldDto } from './create-custom-field.dto';

/**
 * DTO cho việc cập nhật hàng loạt các trường tùy chỉnh của audience
 * Sẽ thay thế hoàn toàn các giá trị hiện có
 */
export class BulkUpdateCustomFieldsDto {
  /**
   * Danh sách các trường tùy chỉnh mới
   * @example [{ fieldId: 1, fieldValue: "Hà Nội" }, { fieldId: 2, fieldValue: 30 }]
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh mới',
    type: [CreateCustomFieldDto],
    example: [
      { fieldId: 1, fieldValue: '<PERSON><PERSON>' },
      { fieldId: 2, fieldValue: 30 },
    ],
  })
  @IsArray({ message: 'Danh sách trường tùy chỉnh phải là một mảng' })
  @ArrayNotEmpty({ message: 'Danh sách trường tùy chỉnh không được để trống' })
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  fields: CreateCustomFieldDto[];
} 