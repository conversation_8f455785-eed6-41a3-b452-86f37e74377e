import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AgentToolsUserService } from '@modules/agent/user/services/agent-tools-user.service';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { AGENT_ERROR_CODES } from '../../exceptions';
import { ErrorCode } from '@/common';
import {
  AgentToolsQueryDto,
  AgentToolsResponseDto,
  AddAgentToolsDto,
  RemoveAgentToolsDto,
  BulkOperationResponseDto,
} from '../dto/agent-tools';

/**
 * Controller xử lý các API endpoint cho Agent Tools của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  AgentToolsResponseDto,
  BulkOperationResponseDto
)
export class AgentToolsUserController {
  constructor(private readonly agentToolsUserService: AgentToolsUserService) { }

  /**
   * Lấy danh sách tools của agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách tools có phân trang
   */
  @Get(':agentId/tools')
  @ApiOperation({ 
    summary: 'Lấy danh sách tools của agent',
    description: 'Lấy danh sách tất cả tools đã được gán cho agent với phân trang'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tools thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentToolsResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_TOOLS_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgentTools(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
    @Query() queryDto: AgentToolsQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentToolsResponseDto>>> {
    const result = await this.agentToolsUserService.getAgentTools(agentId, userId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách tools thành công');
  }

  /**
   * Thêm nhiều tools vào agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param addDto DTO chứa danh sách tool IDs
   * @returns Kết quả thao tác bulk
   */
  @Post(':agentId/tools')
  @ApiOperation({
    summary: 'Thêm nhiều tools vào agent',
    description: 'Thêm nhiều tools vào agent cùng lúc. Tools đã tồn tại sẽ bị bỏ qua.'
  })
  @ApiResponse({
    status: 200,
    description: 'Thêm tools thành công',
    schema: ApiResponseDto.getSchema(BulkOperationResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.TOOLS_NOT_FOUND,
    AGENT_ERROR_CODES.INVALID_TOOL_IDS,
    AGENT_ERROR_CODES.AGENT_TOOLS_ADD_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addToolsToAgent(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
    @Body() addDto: AddAgentToolsDto,
  ): Promise<ApiResponseDto<BulkOperationResponseDto>> {
    const result = await this.agentToolsUserService.addToolsToAgent(agentId, userId, addDto);
    return ApiResponseDto.success(result, 'Thêm tools vào agent thành công');
  }

  /**
   * Gỡ bỏ nhiều tools khỏi agent
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param removeDto DTO chứa danh sách tool IDs
   * @returns Kết quả thao tác bulk
   */
  @Delete(':agentId/tools')
  @ApiOperation({
    summary: 'Gỡ bỏ nhiều tools khỏi agent',
    description: 'Gỡ bỏ nhiều tools khỏi agent cùng lúc. Tools không tồn tại sẽ bị bỏ qua.'
  })
  @ApiResponse({
    status: 200,
    description: 'Gỡ bỏ tools thành công',
    schema: ApiResponseDto.getSchema(BulkOperationResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.INVALID_TOOL_IDS,
    AGENT_ERROR_CODES.AGENT_TOOLS_REMOVE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeToolsFromAgent(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
    @Body() removeDto: RemoveAgentToolsDto,
  ): Promise<ApiResponseDto<BulkOperationResponseDto>> {
    const result = await this.agentToolsUserService.removeToolsFromAgent(agentId, userId, removeDto);
    return ApiResponseDto.success(result, 'Gỡ bỏ tools khỏi agent thành công');
  }
}
