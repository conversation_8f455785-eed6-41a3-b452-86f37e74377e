import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, Min, IsObject } from 'class-validator';

/**
 * DTO cho tạo email campaign với template
 */
export class CreateEmailCampaignWithTemplateDto {
  /**
   * Tiêu đề campaign
   * @example "Khuyến mãi Black Friday 2024"
   */
  @ApiProperty({
    description: 'Tiêu đề campaign',
    example: 'Khuyến mãi Black Friday 2024',
  })
  @IsNotEmpty({ message: 'Tiêu đề không được để trống' })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  title: string;

  /**
   * M<PERSON> tả campaign
   * @example "Chiến dịch email marketing cho sự kiện Black Friday"
   */
  @ApiProperty({
    description: 'Mô tả campaign',
    example: 'Chiến dịch email marketing cho sự kiện Black Friday',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả phải là chuỗi' })
  description?: string;

  /**
   * ID của email template
   * @example "123"
   */
  @ApiProperty({
    description: 'ID của email template',
    example: '123',
  })
  @IsNotEmpty({ message: 'ID template email không được để trống' })
  @IsString({ message: 'ID template email phải là chuỗi' })
  templateEmailId: string;

  /**
   * ID của segment để gửi email
   * @example "456"
   */
  @ApiProperty({
    description: 'ID của segment để gửi email',
    example: '456',
  })
  @IsNotEmpty({ message: 'ID segment không được để trống' })
  @IsString({ message: 'ID segment phải là chuỗi' })
  segmentId: string;

  /**
   * Thời gian dự kiến gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (Unix timestamp). Nếu không có thì gửi ngay lập tức. Phải là thời gian trong tương lai',
    example: 1703980800,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Thời gian gửi phải là số' })
  @Min(Math.floor(Date.now() / 1000), { message: 'Thời gian gửi phải trong tương lai' })
  scheduledAt?: number;

  /**
   * ID của email server configuration
   * @example "789"
   */
  @ApiProperty({
    description: 'ID của email server configuration từ bảng email_server_configurations',
    example: '789',
  })
  @IsNotEmpty({ message: 'ID server không được để trống' })
  @IsString({ message: 'ID server phải là chuỗi' })
  serverId: string;

  /**
   * Cấu hình giá trị cho các biến template
   * @example { "companyName": "RedAI", "discountPercent": "50", "validUntil": "31/12/2024" }
   */
  @ApiProperty({
    description: 'Cấu hình giá trị cho các biến template. Key là tên biến, value là giá trị thay thế',
    example: {
      companyName: 'RedAI',
      discountPercent: '50',
      validUntil: '31/12/2024',
      supportEmail: '<EMAIL>'
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình template phải là object' })
  templateVariables?: Record<string, any>;
}

/**
 * DTO cho phản hồi tạo email campaign với template
 */
export class CreateEmailCampaignWithTemplateResponseDto {
  /**
   * ID của campaign đã tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của campaign đã tạo',
    example: 1,
  })
  campaignId: number;

  /**
   * Thời gian dự kiến gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (Unix timestamp)',
    example: 1703980800,
    required: false,
  })
  scheduledAt?: number;

  /**
   * Trạng thái campaign
   * @example "SCHEDULED"
   */
  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'SCHEDULED',
    enum: ['SENDING', 'SCHEDULED', 'SENT', 'FAILED'],
  })
  status: string;

  /**
   * Thông tin template được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin template được sử dụng',
    example: {
      id: 123,
      name: 'Welcome Email Template',
      subject: 'Chào mừng bạn đến với RedAI'
    },
  })
  template: {
    id: number;
    name: string;
    subject: string;
  };

  /**
   * Thông tin email server được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin email server được sử dụng',
    example: {
      id: 789,
      serverName: 'Gmail SMTP Server',
      host: 'smtp.gmail.com'
    },
  })
  emailServer: {
    id: number;
    serverName: string;
    host: string;
  };
}
